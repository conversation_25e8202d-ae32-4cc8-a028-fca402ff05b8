// frontend/src/main.js
import './styles.css';

// Import libraries
import Alpine from 'alpinejs';
import htmx from 'htmx.org';
import _hyperscript from 'hyperscript.org'; // Updated import

// Real-time notification component
window.notificationComponent = function(initialCount = 0) {
    return {
        isOpen: false,
        hasNewNotifications: false,
        notificationCount: initialCount,
        websocket: null,
        reconnectAttempts: 0,
        maxReconnectAttempts: 5,

        initWebSocket() {
            if (!window.location.protocol.includes('http')) return;

            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            const wsUrl = `${protocol}//${window.location.host}/ws/notifications/`;

            try {
                this.websocket = new WebSocket(wsUrl);

                this.websocket.onopen = () => {
                    console.log('WebSocket connected for notifications');
                    this.reconnectAttempts = 0;
                };

                this.websocket.onmessage = (event) => {
                    const data = JSON.parse(event.data);
                    this.handleWebSocketMessage(data);
                };

                this.websocket.onclose = () => {
                    console.log('WebSocket disconnected');
                    this.attemptReconnect();
                };

                this.websocket.onerror = (error) => {
                    console.error('WebSocket error:', error);
                };

            } catch (error) {
                console.error('Failed to create WebSocket connection:', error);
            }
        },

        handleWebSocketMessage(data) {
            switch (data.type) {
                case 'new_notification':
                    this.handleNewNotification(data.notification);
                    break;
                case 'notification_count':
                    this.updateNotificationCount(data.count);
                    break;
                case 'refresh_list':
                    this.refreshNotificationList();
                    break;
            }
        },

        handleNewNotification(notification) {
            // Show visual feedback for new notification
            this.hasNewNotifications = true;

            // Reset the animation after 3 seconds
            setTimeout(() => {
                this.hasNewNotifications = false;
            }, 3000);

            // Refresh the notification list
            this.refreshNotificationList();

            // Show browser notification if permission granted
            this.showBrowserNotification(notification);
        },

        updateNotificationCount(count) {
            const oldCount = this.notificationCount;
            this.notificationCount = count;

            // Trigger animation if count increased
            if (count > oldCount) {
                this.hasNewNotifications = true;
                setTimeout(() => {
                    this.hasNewNotifications = false;
                }, 3000);
            }
        },

        refreshNotificationList() {
            // Trigger HTMX refresh of notification content
            htmx.trigger(document.body, 'refreshNotifications');
        },

        showBrowserNotification(notification) {
            if ('Notification' in window && Notification.permission === 'granted') {
                new Notification(notification.title, {
                    body: notification.message,
                    icon: '/static/images/logo.png',
                    tag: `notification-${notification.id}`
                });
            }
        },

        toggleDropdown() {
            this.isOpen = !this.isOpen;

            // Reset new notification indicator when opening
            if (this.isOpen) {
                this.hasNewNotifications = false;
            }
        },

        attemptReconnect() {
            if (this.reconnectAttempts < this.maxReconnectAttempts) {
                this.reconnectAttempts++;
                const delay = Math.pow(2, this.reconnectAttempts) * 1000; // Exponential backoff

                console.log(`Attempting to reconnect WebSocket in ${delay}ms (attempt ${this.reconnectAttempts})`);

                setTimeout(() => {
                    this.initWebSocket();
                }, delay);
            } else {
                console.log('Max reconnection attempts reached. Falling back to HTMX polling.');
            }
        },

        requestNotificationPermission() {
            if ('Notification' in window && Notification.permission === 'default') {
                Notification.requestPermission();
            }
        }
    };
};

// Initialize Alpine.js
window.Alpine = Alpine;
Alpine.start();

// Make HTMX available globally
window.htmx = htmx;

// Initialize hyperscript
window._hyperscript = _hyperscript;

// Request notification permission on page load
document.addEventListener('DOMContentLoaded', function() {
    if ('Notification' in window && Notification.permission === 'default') {
        // Request permission after a short delay to avoid immediate popup
        setTimeout(() => {
            Notification.requestPermission();
        }, 2000);
    }
});
