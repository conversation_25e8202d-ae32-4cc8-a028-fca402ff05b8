# Generated by Django 5.1.9 on 2025-05-23 07:10

import django.db.models.deletion
import django.utils.timezone
import model_utils.fields
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        ("contenttypes", "0002_remove_content_type_name"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="Purchase",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "created",
                    model_utils.fields.AutoCreatedField(
                        default=django.utils.timezone.now,
                        editable=False,
                        verbose_name="created",
                    ),
                ),
                (
                    "modified",
                    model_utils.fields.AutoLastModifiedField(
                        default=django.utils.timezone.now,
                        editable=False,
                        verbose_name="modified",
                    ),
                ),
                (
                    "object_id",
                    models.PositiveIntegerField(
                        help_text="The ID of the specific purchasable item"
                    ),
                ),
                ("stripe_payment_intent_id", models.CharField(max_length=100)),
                (
                    "price_paid",
                    models.DecimalField(
                        decimal_places=2,
                        help_text="The price that the user paid. This is stored on save to make sure it doesnt change even if we change the price",
                        max_digits=10,
                    ),
                ),
                ("is_active", models.BooleanField(default=False)),
                (
                    "content_type",
                    models.ForeignKey(
                        help_text="The model type of the purchasable item",
                        on_delete=django.db.models.deletion.CASCADE,
                        to="contenttypes.contenttype",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="purchases",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "unique_together": {("content_type", "object_id", "user")},
            },
        ),
    ]
