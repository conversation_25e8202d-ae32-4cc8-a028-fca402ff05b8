"""
Signal handlers for the main app.
"""
import logging
from django.db.models.signals import post_save
from django.dispatch import receiver
from .models import Notification

logger = logging.getLogger(__name__)


@receiver(post_save, sender=Notification)
def notification_created(sender, instance, created, **kwargs):
    """
    Signal handler that fires when a new notification is created.
    
    This can be extended in the future to:
    - Send WebSocket messages for real-time updates
    - Send push notifications
    - Log notification creation
    - Trigger other real-time events
    
    Args:
        sender: The model class (Notification)
        instance: The actual notification instance
        created: <PERSON><PERSON><PERSON> indicating if this is a new instance
        **kwargs: Additional keyword arguments
    """
    if created:
        logger.info(
            f"New notification created for user {instance.user.username}: {instance.title}"
        )
        
        # Future enhancement: Send WebSocket message
        # This is where you could integrate with Django Channels
        # to send real-time updates to connected clients
        
        # Example of what could be added:
        # from channels.layers import get_channel_layer
        # from asgiref.sync import async_to_sync
        # 
        # channel_layer = get_channel_layer()
        # if channel_layer:
        #     async_to_sync(channel_layer.group_send)(
        #         f"user_{instance.user.id}",
        #         {
        #             "type": "notification_message",
        #             "message": {
        #                 "id": instance.id,
        #                 "title": instance.title,
        #                 "message": instance.message,
        #                 "type": instance.type,
        #                 "created_at": instance.created_at.isoformat(),
        #             }
        #         }
        #     )
