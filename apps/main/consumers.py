"""
WebSocket consumers for real-time notifications.
"""

import json
import logging
from channels.generic.websocket import AsyncWebsocketConsumer
from channels.db import database_sync_to_async
from django.contrib.auth import get_user_model

logger = logging.getLogger(__name__)
User = get_user_model()


class NotificationConsumer(AsyncWebsocketConsumer):
    """
    WebSocket consumer for handling real-time notifications.

    This consumer manages WebSocket connections for authenticated users
    and handles real-time notification updates.
    """

    async def connect(self):
        """
        Handle WebSocket connection.

        Only authenticated users can connect to receive notifications.
        Each user joins their own notification group.
        """
        self.user = self.scope["user"]

        # Only allow authenticated users
        if not self.user.is_authenticated:
            logger.warning(
                "Unauthenticated user attempted to connect to notifications WebSocket"
            )
            await self.close()
            return

        # Create a unique group name for this user's notifications
        self.notification_group_name = f"notifications_{self.user.id}"

        # Join the notification group
        await self.channel_layer.group_add(
            self.notification_group_name, self.channel_name
        )

        # Accept the WebSocket connection
        await self.accept()

        logger.info(f"User {self.user.username} connected to notifications WebSocket")

        # Send initial notification count
        await self.send_notification_count()

    async def disconnect(self, close_code):
        """
        Handle WebSocket disconnection.

        Remove the user from their notification group.
        """
        if hasattr(self, "notification_group_name"):
            await self.channel_layer.group_discard(
                self.notification_group_name, self.channel_name
            )

        logger.info(
            f"User {getattr(self.user, 'username', 'unknown')} disconnected from notifications WebSocket"
        )

    async def receive(self, text_data):
        """
        Handle messages received from WebSocket.

        This can be used for client-side actions like marking notifications as read.
        """
        try:
            text_data_json = json.loads(text_data)
            message_type = text_data_json.get("type")

            if message_type == "mark_as_read":
                notification_id = text_data_json.get("notification_id")
                if notification_id:
                    await self.mark_notification_as_read(notification_id)

            elif message_type == "get_count":
                await self.send_notification_count()

        except json.JSONDecodeError:
            logger.error("Invalid JSON received in WebSocket message")
        except Exception as e:
            logger.error(f"Error handling WebSocket message: {e}")

    async def notification_message(self, event):
        """
        Handle notification messages sent to the group.

        This method is called when a new notification is created
        and sent to the user's notification group.
        """
        message = event["message"]

        # Send the notification data to the WebSocket
        await self.send(
            text_data=json.dumps({"type": "new_notification", "notification": message})
        )

        # Also send updated count
        await self.send_notification_count()

        logger.info(
            f"Sent real-time notification to user {self.user.username}: {message['title']}"
        )

    @database_sync_to_async
    def get_notification_count(self):
        """
        Get the current unread notification count for the user.
        """
        return self.user.notifications.filter(is_read=False).count()

    @database_sync_to_async
    def mark_notification_as_read(self, notification_id):
        """
        Mark a specific notification as read.
        """
        try:
            notification = self.user.notifications.get(
                id=notification_id, is_read=False
            )
            notification.mark_as_read()
            logger.info(
                f"Marked notification {notification_id} as read for user {self.user.username}"
            )
            return True
        except Exception as e:
            logger.error(f"Failed to mark notification {notification_id} as read: {e}")
            return False

    async def send_notification_count(self):
        """
        Send the current notification count to the client.
        """
        count = await self.get_notification_count()
        await self.send(
            text_data=json.dumps({"type": "notification_count", "count": count})
        )


class NotificationListConsumer(AsyncWebsocketConsumer):
    """
    WebSocket consumer for handling notification list updates.

    This consumer provides real-time updates to the notification dropdown list.
    """

    async def connect(self):
        """Handle WebSocket connection for notification list updates."""
        self.user = self.scope["user"]

        if not self.user.is_authenticated:
            await self.close()
            return

        self.notification_group_name = f"notifications_{self.user.id}"

        await self.channel_layer.group_add(
            self.notification_group_name, self.channel_name
        )

        await self.accept()
        logger.info(
            f"User {self.user.username} connected to notification list WebSocket"
        )

    async def disconnect(self, close_code):
        """Handle WebSocket disconnection."""
        if hasattr(self, "notification_group_name"):
            await self.channel_layer.group_discard(
                self.notification_group_name, self.channel_name
            )

    async def notification_message(self, event):
        """
        Handle notification messages for list updates.

        Sends a signal to refresh the notification list.
        """
        await self.send(
            text_data=json.dumps(
                {
                    "type": "refresh_list",
                    "message": "New notification received, refresh list",
                }
            )
        )
