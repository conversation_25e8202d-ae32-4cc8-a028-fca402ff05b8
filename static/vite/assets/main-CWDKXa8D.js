var Ve=Object.defineProperty;var We=(e,t,n)=>t in e?Ve(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n;var de=(e,t,n)=>We(e,typeof t!="symbol"?t+"":t,n);var flushPending=!1,flushing=!1,queue=[],lastFlushedIndex=-1;function scheduler(e){queueJob(e)}function queueJob(e){queue.includes(e)||queue.push(e),queueFlush()}function dequeueJob(e){let t=queue.indexOf(e);t!==-1&&t>lastFlushedIndex&&queue.splice(t,1)}function queueFlush(){!flushing&&!flushPending&&(flushPending=!0,queueMicrotask(flushJobs))}function flushJobs(){flushPending=!1,flushing=!0;for(let e=0;e<queue.length;e++)queue[e](),lastFlushedIndex=e;queue.length=0,lastFlushedIndex=-1,flushing=!1}var reactive,effect,release,raw,shouldSchedule=!0;function disableEffectScheduling(e){shouldSchedule=!1,e(),shouldSchedule=!0}function setReactivityEngine(e){reactive=e.reactive,release=e.release,effect=t=>e.effect(t,{scheduler:n=>{shouldSchedule?scheduler(n):n()}}),raw=e.raw}function overrideEffect(e){effect=e}function elementBoundEffect(e){let t=()=>{};return[r=>{let a=effect(r);return e._x_effects||(e._x_effects=new Set,e._x_runEffects=()=>{e._x_effects.forEach(o=>o())}),e._x_effects.add(a),t=()=>{a!==void 0&&(e._x_effects.delete(a),release(a))},a},()=>{t()}]}function watch(e,t){let n=!0,r,a=effect(()=>{let o=e();JSON.stringify(o),n?r=o:queueMicrotask(()=>{t(o,r),r=o}),n=!1});return()=>release(a)}var onAttributeAddeds=[],onElRemoveds=[],onElAddeds=[];function onElAdded(e){onElAddeds.push(e)}function onElRemoved(e,t){typeof t=="function"?(e._x_cleanups||(e._x_cleanups=[]),e._x_cleanups.push(t)):(t=e,onElRemoveds.push(t))}function onAttributesAdded(e){onAttributeAddeds.push(e)}function onAttributeRemoved(e,t,n){e._x_attributeCleanups||(e._x_attributeCleanups={}),e._x_attributeCleanups[t]||(e._x_attributeCleanups[t]=[]),e._x_attributeCleanups[t].push(n)}function cleanupAttributes(e,t){e._x_attributeCleanups&&Object.entries(e._x_attributeCleanups).forEach(([n,r])=>{(t===void 0||t.includes(n))&&(r.forEach(a=>a()),delete e._x_attributeCleanups[n])})}function cleanupElement(e){var t,n;for((t=e._x_effects)==null||t.forEach(dequeueJob);(n=e._x_cleanups)!=null&&n.length;)e._x_cleanups.pop()()}var observer=new MutationObserver(onMutate),currentlyObserving=!1;function startObservingMutations(){observer.observe(document,{subtree:!0,childList:!0,attributes:!0,attributeOldValue:!0}),currentlyObserving=!0}function stopObservingMutations(){flushObserver(),observer.disconnect(),currentlyObserving=!1}var queuedMutations=[];function flushObserver(){let e=observer.takeRecords();queuedMutations.push(()=>e.length>0&&onMutate(e));let t=queuedMutations.length;queueMicrotask(()=>{if(queuedMutations.length===t)for(;queuedMutations.length>0;)queuedMutations.shift()()})}function mutateDom(e){if(!currentlyObserving)return e();stopObservingMutations();let t=e();return startObservingMutations(),t}var isCollecting=!1,deferredMutations=[];function deferMutations(){isCollecting=!0}function flushAndStopDeferringMutations(){isCollecting=!1,onMutate(deferredMutations),deferredMutations=[]}function onMutate(e){if(isCollecting){deferredMutations=deferredMutations.concat(e);return}let t=[],n=new Set,r=new Map,a=new Map;for(let o=0;o<e.length;o++)if(!e[o].target._x_ignoreMutationObserver&&(e[o].type==="childList"&&(e[o].removedNodes.forEach(u=>{u.nodeType===1&&u._x_marker&&n.add(u)}),e[o].addedNodes.forEach(u=>{if(u.nodeType===1){if(n.has(u)){n.delete(u);return}u._x_marker||t.push(u)}})),e[o].type==="attributes")){let u=e[o].target,E=e[o].attributeName,b=e[o].oldValue,_=()=>{r.has(u)||r.set(u,[]),r.get(u).push({name:E,value:u.getAttribute(E)})},P=()=>{a.has(u)||a.set(u,[]),a.get(u).push(E)};u.hasAttribute(E)&&b===null?_():u.hasAttribute(E)?(P(),_()):P()}a.forEach((o,u)=>{cleanupAttributes(u,o)}),r.forEach((o,u)=>{onAttributeAddeds.forEach(E=>E(u,o))});for(let o of n)t.some(u=>u.contains(o))||onElRemoveds.forEach(u=>u(o));for(let o of t)o.isConnected&&onElAddeds.forEach(u=>u(o));t=null,n=null,r=null,a=null}function scope(e){return mergeProxies(closestDataStack(e))}function addScopeToNode(e,t,n){return e._x_dataStack=[t,...closestDataStack(n||e)],()=>{e._x_dataStack=e._x_dataStack.filter(r=>r!==t)}}function closestDataStack(e){return e._x_dataStack?e._x_dataStack:typeof ShadowRoot=="function"&&e instanceof ShadowRoot?closestDataStack(e.host):e.parentNode?closestDataStack(e.parentNode):[]}function mergeProxies(e){return new Proxy({objects:e},mergeProxyTrap)}var mergeProxyTrap={ownKeys({objects:e}){return Array.from(new Set(e.flatMap(t=>Object.keys(t))))},has({objects:e},t){return t==Symbol.unscopables?!1:e.some(n=>Object.prototype.hasOwnProperty.call(n,t)||Reflect.has(n,t))},get({objects:e},t,n){return t=="toJSON"?collapseProxies:Reflect.get(e.find(r=>Reflect.has(r,t))||{},t,n)},set({objects:e},t,n,r){const a=e.find(u=>Object.prototype.hasOwnProperty.call(u,t))||e[e.length-1],o=Object.getOwnPropertyDescriptor(a,t);return o!=null&&o.set&&(o!=null&&o.get)?o.set.call(r,n)||!0:Reflect.set(a,t,n)}};function collapseProxies(){return Reflect.ownKeys(this).reduce((t,n)=>(t[n]=Reflect.get(this,n),t),{})}function initInterceptors(e){let t=r=>typeof r=="object"&&!Array.isArray(r)&&r!==null,n=(r,a="")=>{Object.entries(Object.getOwnPropertyDescriptors(r)).forEach(([o,{value:u,enumerable:E}])=>{if(E===!1||u===void 0||typeof u=="object"&&u!==null&&u.__v_skip)return;let b=a===""?o:`${a}.${o}`;typeof u=="object"&&u!==null&&u._x_interceptor?r[o]=u.initialize(e,b,o):t(u)&&u!==r&&!(u instanceof Element)&&n(u,b)})};return n(e)}function interceptor(e,t=()=>{}){let n={initialValue:void 0,_x_interceptor:!0,initialize(r,a,o){return e(this.initialValue,()=>get(r,a),u=>set(r,a,u),a,o)}};return t(n),r=>{if(typeof r=="object"&&r!==null&&r._x_interceptor){let a=n.initialize.bind(n);n.initialize=(o,u,E)=>{let b=r.initialize(o,u,E);return n.initialValue=b,a(o,u,E)}}else n.initialValue=r;return n}}function get(e,t){return t.split(".").reduce((n,r)=>n[r],e)}function set(e,t,n){if(typeof t=="string"&&(t=t.split(".")),t.length===1)e[t[0]]=n;else{if(t.length===0)throw error;return e[t[0]]||(e[t[0]]={}),set(e[t[0]],t.slice(1),n)}}var magics={};function magic(e,t){magics[e]=t}function injectMagics(e,t){let n=getUtilities(t);return Object.entries(magics).forEach(([r,a])=>{Object.defineProperty(e,`$${r}`,{get(){return a(t,n)},enumerable:!1})}),e}function getUtilities(e){let[t,n]=getElementBoundUtilities(e),r={interceptor,...t};return onElRemoved(e,n),r}function tryCatch(e,t,n,...r){try{return n(...r)}catch(a){handleError(a,e,t)}}function handleError(e,t,n=void 0){e=Object.assign(e??{message:"No error message given."},{el:t,expression:n}),console.warn(`Alpine Expression Error: ${e.message}

${n?'Expression: "'+n+`"

`:""}`,t),setTimeout(()=>{throw e},0)}var shouldAutoEvaluateFunctions=!0;function dontAutoEvaluateFunctions(e){let t=shouldAutoEvaluateFunctions;shouldAutoEvaluateFunctions=!1;let n=e();return shouldAutoEvaluateFunctions=t,n}function evaluate(e,t,n={}){let r;return evaluateLater(e,t)(a=>r=a,n),r}function evaluateLater(...e){return theEvaluatorFunction(...e)}var theEvaluatorFunction=normalEvaluator;function setEvaluator(e){theEvaluatorFunction=e}function normalEvaluator(e,t){let n={};injectMagics(n,e);let r=[n,...closestDataStack(e)],a=typeof t=="function"?generateEvaluatorFromFunction(r,t):generateEvaluatorFromString(r,t,e);return tryCatch.bind(null,e,t,a)}function generateEvaluatorFromFunction(e,t){return(n=()=>{},{scope:r={},params:a=[]}={})=>{let o=t.apply(mergeProxies([r,...e]),a);runIfTypeOfFunction(n,o)}}var evaluatorMemo={};function generateFunctionFromString(e,t){if(evaluatorMemo[e])return evaluatorMemo[e];let n=Object.getPrototypeOf(async function(){}).constructor,r=/^[\n\s]*if.*\(.*\)/.test(e.trim())||/^(let|const)\s/.test(e.trim())?`(async()=>{ ${e} })()`:e,o=(()=>{try{let u=new n(["__self","scope"],`with (scope) { __self.result = ${r} }; __self.finished = true; return __self.result;`);return Object.defineProperty(u,"name",{value:`[Alpine] ${e}`}),u}catch(u){return handleError(u,t,e),Promise.resolve()}})();return evaluatorMemo[e]=o,o}function generateEvaluatorFromString(e,t,n){let r=generateFunctionFromString(t,n);return(a=()=>{},{scope:o={},params:u=[]}={})=>{r.result=void 0,r.finished=!1;let E=mergeProxies([o,...e]);if(typeof r=="function"){let b=r(r,E).catch(_=>handleError(_,n,t));r.finished?(runIfTypeOfFunction(a,r.result,E,u,n),r.result=void 0):b.then(_=>{runIfTypeOfFunction(a,_,E,u,n)}).catch(_=>handleError(_,n,t)).finally(()=>r.result=void 0)}}}function runIfTypeOfFunction(e,t,n,r,a){if(shouldAutoEvaluateFunctions&&typeof t=="function"){let o=t.apply(n,r);o instanceof Promise?o.then(u=>runIfTypeOfFunction(e,u,n,r)).catch(u=>handleError(u,a,t)):e(o)}else typeof t=="object"&&t instanceof Promise?t.then(o=>e(o)):e(t)}var prefixAsString="x-";function prefix(e=""){return prefixAsString+e}function setPrefix(e){prefixAsString=e}var directiveHandlers={};function directive(e,t){return directiveHandlers[e]=t,{before(n){if(!directiveHandlers[n]){console.warn(String.raw`Cannot find directive \`${n}\`. \`${e}\` will use the default order of execution`);return}const r=directiveOrder.indexOf(n);directiveOrder.splice(r>=0?r:directiveOrder.indexOf("DEFAULT"),0,e)}}}function directiveExists(e){return Object.keys(directiveHandlers).includes(e)}function directives(e,t,n){if(t=Array.from(t),e._x_virtualDirectives){let o=Object.entries(e._x_virtualDirectives).map(([E,b])=>({name:E,value:b})),u=attributesOnly(o);o=o.map(E=>u.find(b=>b.name===E.name)?{name:`x-bind:${E.name}`,value:`"${E.value}"`}:E),t=t.concat(o)}let r={};return t.map(toTransformedAttributes((o,u)=>r[o]=u)).filter(outNonAlpineAttributes).map(toParsedDirectives(r,n)).sort(byPriority).map(o=>getDirectiveHandler(e,o))}function attributesOnly(e){return Array.from(e).map(toTransformedAttributes()).filter(t=>!outNonAlpineAttributes(t))}var isDeferringHandlers=!1,directiveHandlerStacks=new Map,currentHandlerStackKey=Symbol();function deferHandlingDirectives(e){isDeferringHandlers=!0;let t=Symbol();currentHandlerStackKey=t,directiveHandlerStacks.set(t,[]);let n=()=>{for(;directiveHandlerStacks.get(t).length;)directiveHandlerStacks.get(t).shift()();directiveHandlerStacks.delete(t)},r=()=>{isDeferringHandlers=!1,n()};e(n),r()}function getElementBoundUtilities(e){let t=[],n=E=>t.push(E),[r,a]=elementBoundEffect(e);return t.push(a),[{Alpine:alpine_default,effect:r,cleanup:n,evaluateLater:evaluateLater.bind(evaluateLater,e),evaluate:evaluate.bind(evaluate,e)},()=>t.forEach(E=>E())]}function getDirectiveHandler(e,t){let n=()=>{},r=directiveHandlers[t.type]||n,[a,o]=getElementBoundUtilities(e);onAttributeRemoved(e,t.original,o);let u=()=>{e._x_ignore||e._x_ignoreSelf||(r.inline&&r.inline(e,t,a),r=r.bind(r,e,t,a),isDeferringHandlers?directiveHandlerStacks.get(currentHandlerStackKey).push(r):r())};return u.runCleanups=o,u}var startingWith=(e,t)=>({name:n,value:r})=>(n.startsWith(e)&&(n=n.replace(e,t)),{name:n,value:r}),into=e=>e;function toTransformedAttributes(e=()=>{}){return({name:t,value:n})=>{let{name:r,value:a}=attributeTransformers.reduce((o,u)=>u(o),{name:t,value:n});return r!==t&&e(r,t),{name:r,value:a}}}var attributeTransformers=[];function mapAttributes(e){attributeTransformers.push(e)}function outNonAlpineAttributes({name:e}){return alpineAttributeRegex().test(e)}var alpineAttributeRegex=()=>new RegExp(`^${prefixAsString}([^:^.]+)\\b`);function toParsedDirectives(e,t){return({name:n,value:r})=>{let a=n.match(alpineAttributeRegex()),o=n.match(/:([a-zA-Z0-9\-_:]+)/),u=n.match(/\.[^.\]]+(?=[^\]]*$)/g)||[],E=t||e[n]||n;return{type:a?a[1]:null,value:o?o[1]:null,modifiers:u.map(b=>b.replace(".","")),expression:r,original:E}}}var DEFAULT="DEFAULT",directiveOrder=["ignore","ref","data","id","anchor","bind","init","for","model","modelable","transition","show","if",DEFAULT,"teleport"];function byPriority(e,t){let n=directiveOrder.indexOf(e.type)===-1?DEFAULT:e.type,r=directiveOrder.indexOf(t.type)===-1?DEFAULT:t.type;return directiveOrder.indexOf(n)-directiveOrder.indexOf(r)}function dispatch(e,t,n={}){e.dispatchEvent(new CustomEvent(t,{detail:n,bubbles:!0,composed:!0,cancelable:!0}))}function walk(e,t){if(typeof ShadowRoot=="function"&&e instanceof ShadowRoot){Array.from(e.children).forEach(a=>walk(a,t));return}let n=!1;if(t(e,()=>n=!0),n)return;let r=e.firstElementChild;for(;r;)walk(r,t),r=r.nextElementSibling}function warn(e,...t){console.warn(`Alpine Warning: ${e}`,...t)}var started=!1;function start(){started&&warn("Alpine has already been initialized on this page. Calling Alpine.start() more than once can cause problems."),started=!0,document.body||warn("Unable to initialize. Trying to load Alpine before `<body>` is available. Did you forget to add `defer` in Alpine's `<script>` tag?"),dispatch(document,"alpine:init"),dispatch(document,"alpine:initializing"),startObservingMutations(),onElAdded(t=>initTree(t,walk)),onElRemoved(t=>destroyTree(t)),onAttributesAdded((t,n)=>{directives(t,n).forEach(r=>r())});let e=t=>!closestRoot(t.parentElement,!0);Array.from(document.querySelectorAll(allSelectors().join(","))).filter(e).forEach(t=>{initTree(t)}),dispatch(document,"alpine:initialized"),setTimeout(()=>{warnAboutMissingPlugins()})}var rootSelectorCallbacks=[],initSelectorCallbacks=[];function rootSelectors(){return rootSelectorCallbacks.map(e=>e())}function allSelectors(){return rootSelectorCallbacks.concat(initSelectorCallbacks).map(e=>e())}function addRootSelector(e){rootSelectorCallbacks.push(e)}function addInitSelector(e){initSelectorCallbacks.push(e)}function closestRoot(e,t=!1){return findClosest(e,n=>{if((t?allSelectors():rootSelectors()).some(a=>n.matches(a)))return!0})}function findClosest(e,t){if(e){if(t(e))return e;if(e._x_teleportBack&&(e=e._x_teleportBack),!!e.parentElement)return findClosest(e.parentElement,t)}}function isRoot(e){return rootSelectors().some(t=>e.matches(t))}var initInterceptors2=[];function interceptInit(e){initInterceptors2.push(e)}var markerDispenser=1;function initTree(e,t=walk,n=()=>{}){findClosest(e,r=>r._x_ignore)||deferHandlingDirectives(()=>{t(e,(r,a)=>{r._x_marker||(n(r,a),initInterceptors2.forEach(o=>o(r,a)),directives(r,r.attributes).forEach(o=>o()),r._x_ignore||(r._x_marker=markerDispenser++),r._x_ignore&&a())})})}function destroyTree(e,t=walk){t(e,n=>{cleanupElement(n),cleanupAttributes(n),delete n._x_marker})}function warnAboutMissingPlugins(){[["ui","dialog",["[x-dialog], [x-popover]"]],["anchor","anchor",["[x-anchor]"]],["sort","sort",["[x-sort]"]]].forEach(([t,n,r])=>{directiveExists(n)||r.some(a=>{if(document.querySelector(a))return warn(`found "${a}", but missing ${t} plugin`),!0})})}var tickStack=[],isHolding=!1;function nextTick(e=()=>{}){return queueMicrotask(()=>{isHolding||setTimeout(()=>{releaseNextTicks()})}),new Promise(t=>{tickStack.push(()=>{e(),t()})})}function releaseNextTicks(){for(isHolding=!1;tickStack.length;)tickStack.shift()()}function holdNextTicks(){isHolding=!0}function setClasses(e,t){return Array.isArray(t)?setClassesFromString(e,t.join(" ")):typeof t=="object"&&t!==null?setClassesFromObject(e,t):typeof t=="function"?setClasses(e,t()):setClassesFromString(e,t)}function setClassesFromString(e,t){let n=a=>a.split(" ").filter(o=>!e.classList.contains(o)).filter(Boolean),r=a=>(e.classList.add(...a),()=>{e.classList.remove(...a)});return t=t===!0?t="":t||"",r(n(t))}function setClassesFromObject(e,t){let n=E=>E.split(" ").filter(Boolean),r=Object.entries(t).flatMap(([E,b])=>b?n(E):!1).filter(Boolean),a=Object.entries(t).flatMap(([E,b])=>b?!1:n(E)).filter(Boolean),o=[],u=[];return a.forEach(E=>{e.classList.contains(E)&&(e.classList.remove(E),u.push(E))}),r.forEach(E=>{e.classList.contains(E)||(e.classList.add(E),o.push(E))}),()=>{u.forEach(E=>e.classList.add(E)),o.forEach(E=>e.classList.remove(E))}}function setStyles(e,t){return typeof t=="object"&&t!==null?setStylesFromObject(e,t):setStylesFromString(e,t)}function setStylesFromObject(e,t){let n={};return Object.entries(t).forEach(([r,a])=>{n[r]=e.style[r],r.startsWith("--")||(r=kebabCase(r)),e.style.setProperty(r,a)}),setTimeout(()=>{e.style.length===0&&e.removeAttribute("style")}),()=>{setStyles(e,n)}}function setStylesFromString(e,t){let n=e.getAttribute("style",t);return e.setAttribute("style",t),()=>{e.setAttribute("style",n||"")}}function kebabCase(e){return e.replace(/([a-z])([A-Z])/g,"$1-$2").toLowerCase()}function once(e,t=()=>{}){let n=!1;return function(){n?t.apply(this,arguments):(n=!0,e.apply(this,arguments))}}directive("transition",(e,{value:t,modifiers:n,expression:r},{evaluate:a})=>{typeof r=="function"&&(r=a(r)),r!==!1&&(!r||typeof r=="boolean"?registerTransitionsFromHelper(e,n,t):registerTransitionsFromClassString(e,r,t))});function registerTransitionsFromClassString(e,t,n){registerTransitionObject(e,setClasses,""),{enter:a=>{e._x_transition.enter.during=a},"enter-start":a=>{e._x_transition.enter.start=a},"enter-end":a=>{e._x_transition.enter.end=a},leave:a=>{e._x_transition.leave.during=a},"leave-start":a=>{e._x_transition.leave.start=a},"leave-end":a=>{e._x_transition.leave.end=a}}[n](t)}function registerTransitionsFromHelper(e,t,n){registerTransitionObject(e,setStyles);let r=!t.includes("in")&&!t.includes("out")&&!n,a=r||t.includes("in")||["enter"].includes(n),o=r||t.includes("out")||["leave"].includes(n);t.includes("in")&&!r&&(t=t.filter((Q,te)=>te<t.indexOf("out"))),t.includes("out")&&!r&&(t=t.filter((Q,te)=>te>t.indexOf("out")));let u=!t.includes("opacity")&&!t.includes("scale"),E=u||t.includes("opacity"),b=u||t.includes("scale"),_=E?0:1,P=b?modifierValue(t,"scale",95)/100:1,j=modifierValue(t,"delay",0)/1e3,$=modifierValue(t,"origin","center"),J="opacity, transform",re=modifierValue(t,"duration",150)/1e3,ce=modifierValue(t,"duration",75)/1e3,W="cubic-bezier(0.4, 0.0, 0.2, 1)";a&&(e._x_transition.enter.during={transformOrigin:$,transitionDelay:`${j}s`,transitionProperty:J,transitionDuration:`${re}s`,transitionTimingFunction:W},e._x_transition.enter.start={opacity:_,transform:`scale(${P})`},e._x_transition.enter.end={opacity:1,transform:"scale(1)"}),o&&(e._x_transition.leave.during={transformOrigin:$,transitionDelay:`${j}s`,transitionProperty:J,transitionDuration:`${ce}s`,transitionTimingFunction:W},e._x_transition.leave.start={opacity:1,transform:"scale(1)"},e._x_transition.leave.end={opacity:_,transform:`scale(${P})`})}function registerTransitionObject(e,t,n={}){e._x_transition||(e._x_transition={enter:{during:n,start:n,end:n},leave:{during:n,start:n,end:n},in(r=()=>{},a=()=>{}){transition(e,t,{during:this.enter.during,start:this.enter.start,end:this.enter.end},r,a)},out(r=()=>{},a=()=>{}){transition(e,t,{during:this.leave.during,start:this.leave.start,end:this.leave.end},r,a)}})}window.Element.prototype._x_toggleAndCascadeWithTransitions=function(e,t,n,r){const a=document.visibilityState==="visible"?requestAnimationFrame:setTimeout;let o=()=>a(n);if(t){e._x_transition&&(e._x_transition.enter||e._x_transition.leave)?e._x_transition.enter&&(Object.entries(e._x_transition.enter.during).length||Object.entries(e._x_transition.enter.start).length||Object.entries(e._x_transition.enter.end).length)?e._x_transition.in(n):o():e._x_transition?e._x_transition.in(n):o();return}e._x_hidePromise=e._x_transition?new Promise((u,E)=>{e._x_transition.out(()=>{},()=>u(r)),e._x_transitioning&&e._x_transitioning.beforeCancel(()=>E({isFromCancelledTransition:!0}))}):Promise.resolve(r),queueMicrotask(()=>{let u=closestHide(e);u?(u._x_hideChildren||(u._x_hideChildren=[]),u._x_hideChildren.push(e)):a(()=>{let E=b=>{let _=Promise.all([b._x_hidePromise,...(b._x_hideChildren||[]).map(E)]).then(([P])=>P==null?void 0:P());return delete b._x_hidePromise,delete b._x_hideChildren,_};E(e).catch(b=>{if(!b.isFromCancelledTransition)throw b})})})};function closestHide(e){let t=e.parentNode;if(t)return t._x_hidePromise?t:closestHide(t)}function transition(e,t,{during:n,start:r,end:a}={},o=()=>{},u=()=>{}){if(e._x_transitioning&&e._x_transitioning.cancel(),Object.keys(n).length===0&&Object.keys(r).length===0&&Object.keys(a).length===0){o(),u();return}let E,b,_;performTransition(e,{start(){E=t(e,r)},during(){b=t(e,n)},before:o,end(){E(),_=t(e,a)},after:u,cleanup(){b(),_()}})}function performTransition(e,t){let n,r,a,o=once(()=>{mutateDom(()=>{n=!0,r||t.before(),a||(t.end(),releaseNextTicks()),t.after(),e.isConnected&&t.cleanup(),delete e._x_transitioning})});e._x_transitioning={beforeCancels:[],beforeCancel(u){this.beforeCancels.push(u)},cancel:once(function(){for(;this.beforeCancels.length;)this.beforeCancels.shift()();o()}),finish:o},mutateDom(()=>{t.start(),t.during()}),holdNextTicks(),requestAnimationFrame(()=>{if(n)return;let u=Number(getComputedStyle(e).transitionDuration.replace(/,.*/,"").replace("s",""))*1e3,E=Number(getComputedStyle(e).transitionDelay.replace(/,.*/,"").replace("s",""))*1e3;u===0&&(u=Number(getComputedStyle(e).animationDuration.replace("s",""))*1e3),mutateDom(()=>{t.before()}),r=!0,requestAnimationFrame(()=>{n||(mutateDom(()=>{t.end()}),releaseNextTicks(),setTimeout(e._x_transitioning.finish,u+E),a=!0)})})}function modifierValue(e,t,n){if(e.indexOf(t)===-1)return n;const r=e[e.indexOf(t)+1];if(!r||t==="scale"&&isNaN(r))return n;if(t==="duration"||t==="delay"){let a=r.match(/([0-9]+)ms/);if(a)return a[1]}return t==="origin"&&["top","right","left","center","bottom"].includes(e[e.indexOf(t)+2])?[r,e[e.indexOf(t)+2]].join(" "):r}var isCloning=!1;function skipDuringClone(e,t=()=>{}){return(...n)=>isCloning?t(...n):e(...n)}function onlyDuringClone(e){return(...t)=>isCloning&&e(...t)}var interceptors=[];function interceptClone(e){interceptors.push(e)}function cloneNode(e,t){interceptors.forEach(n=>n(e,t)),isCloning=!0,dontRegisterReactiveSideEffects(()=>{initTree(t,(n,r)=>{r(n,()=>{})})}),isCloning=!1}var isCloningLegacy=!1;function clone(e,t){t._x_dataStack||(t._x_dataStack=e._x_dataStack),isCloning=!0,isCloningLegacy=!0,dontRegisterReactiveSideEffects(()=>{cloneTree(t)}),isCloning=!1,isCloningLegacy=!1}function cloneTree(e){let t=!1;initTree(e,(r,a)=>{walk(r,(o,u)=>{if(t&&isRoot(o))return u();t=!0,a(o,u)})})}function dontRegisterReactiveSideEffects(e){let t=effect;overrideEffect((n,r)=>{let a=t(n);return release(a),()=>{}}),e(),overrideEffect(t)}function bind(e,t,n,r=[]){switch(e._x_bindings||(e._x_bindings=reactive({})),e._x_bindings[t]=n,t=r.includes("camel")?camelCase(t):t,t){case"value":bindInputValue(e,n);break;case"style":bindStyles(e,n);break;case"class":bindClasses(e,n);break;case"selected":case"checked":bindAttributeAndProperty(e,t,n);break;default:bindAttribute(e,t,n);break}}function bindInputValue(e,t){if(isRadio(e))e.attributes.value===void 0&&(e.value=t),window.fromModel&&(typeof t=="boolean"?e.checked=safeParseBoolean(e.value)===t:e.checked=checkedAttrLooseCompare(e.value,t));else if(isCheckbox(e))Number.isInteger(t)?e.value=t:!Array.isArray(t)&&typeof t!="boolean"&&![null,void 0].includes(t)?e.value=String(t):Array.isArray(t)?e.checked=t.some(n=>checkedAttrLooseCompare(n,e.value)):e.checked=!!t;else if(e.tagName==="SELECT")updateSelect(e,t);else{if(e.value===t)return;e.value=t===void 0?"":t}}function bindClasses(e,t){e._x_undoAddedClasses&&e._x_undoAddedClasses(),e._x_undoAddedClasses=setClasses(e,t)}function bindStyles(e,t){e._x_undoAddedStyles&&e._x_undoAddedStyles(),e._x_undoAddedStyles=setStyles(e,t)}function bindAttributeAndProperty(e,t,n){bindAttribute(e,t,n),setPropertyIfChanged(e,t,n)}function bindAttribute(e,t,n){[null,void 0,!1].includes(n)&&attributeShouldntBePreservedIfFalsy(t)?e.removeAttribute(t):(isBooleanAttr(t)&&(n=t),setIfChanged(e,t,n))}function setIfChanged(e,t,n){e.getAttribute(t)!=n&&e.setAttribute(t,n)}function setPropertyIfChanged(e,t,n){e[t]!==n&&(e[t]=n)}function updateSelect(e,t){const n=[].concat(t).map(r=>r+"");Array.from(e.options).forEach(r=>{r.selected=n.includes(r.value)})}function camelCase(e){return e.toLowerCase().replace(/-(\w)/g,(t,n)=>n.toUpperCase())}function checkedAttrLooseCompare(e,t){return e==t}function safeParseBoolean(e){return[1,"1","true","on","yes",!0].includes(e)?!0:[0,"0","false","off","no",!1].includes(e)?!1:e?!!e:null}var booleanAttributes=new Set(["allowfullscreen","async","autofocus","autoplay","checked","controls","default","defer","disabled","formnovalidate","inert","ismap","itemscope","loop","multiple","muted","nomodule","novalidate","open","playsinline","readonly","required","reversed","selected","shadowrootclonable","shadowrootdelegatesfocus","shadowrootserializable"]);function isBooleanAttr(e){return booleanAttributes.has(e)}function attributeShouldntBePreservedIfFalsy(e){return!["aria-pressed","aria-checked","aria-expanded","aria-selected"].includes(e)}function getBinding(e,t,n){return e._x_bindings&&e._x_bindings[t]!==void 0?e._x_bindings[t]:getAttributeBinding(e,t,n)}function extractProp(e,t,n,r=!0){if(e._x_bindings&&e._x_bindings[t]!==void 0)return e._x_bindings[t];if(e._x_inlineBindings&&e._x_inlineBindings[t]!==void 0){let a=e._x_inlineBindings[t];return a.extract=r,dontAutoEvaluateFunctions(()=>evaluate(e,a.expression))}return getAttributeBinding(e,t,n)}function getAttributeBinding(e,t,n){let r=e.getAttribute(t);return r===null?typeof n=="function"?n():n:r===""?!0:isBooleanAttr(t)?!![t,"true"].includes(r):r}function isCheckbox(e){return e.type==="checkbox"||e.localName==="ui-checkbox"||e.localName==="ui-switch"}function isRadio(e){return e.type==="radio"||e.localName==="ui-radio"}function debounce(e,t){var n;return function(){var r=this,a=arguments,o=function(){n=null,e.apply(r,a)};clearTimeout(n),n=setTimeout(o,t)}}function throttle(e,t){let n;return function(){let r=this,a=arguments;n||(e.apply(r,a),n=!0,setTimeout(()=>n=!1,t))}}function entangle({get:e,set:t},{get:n,set:r}){let a=!0,o,u=effect(()=>{let E=e(),b=n();if(a)r(cloneIfObject(E)),a=!1;else{let _=JSON.stringify(E),P=JSON.stringify(b);_!==o?r(cloneIfObject(E)):_!==P&&t(cloneIfObject(b))}o=JSON.stringify(e()),JSON.stringify(n())});return()=>{release(u)}}function cloneIfObject(e){return typeof e=="object"?JSON.parse(JSON.stringify(e)):e}function plugin(e){(Array.isArray(e)?e:[e]).forEach(n=>n(alpine_default))}var stores={},isReactive=!1;function store(e,t){if(isReactive||(stores=reactive(stores),isReactive=!0),t===void 0)return stores[e];stores[e]=t,initInterceptors(stores[e]),typeof t=="object"&&t!==null&&t.hasOwnProperty("init")&&typeof t.init=="function"&&stores[e].init()}function getStores(){return stores}var binds={};function bind2(e,t){let n=typeof t!="function"?()=>t:t;return e instanceof Element?applyBindingsObject(e,n()):(binds[e]=n,()=>{})}function injectBindingProviders(e){return Object.entries(binds).forEach(([t,n])=>{Object.defineProperty(e,t,{get(){return(...r)=>n(...r)}})}),e}function applyBindingsObject(e,t,n){let r=[];for(;r.length;)r.pop()();let a=Object.entries(t).map(([u,E])=>({name:u,value:E})),o=attributesOnly(a);return a=a.map(u=>o.find(E=>E.name===u.name)?{name:`x-bind:${u.name}`,value:`"${u.value}"`}:u),directives(e,a,n).map(u=>{r.push(u.runCleanups),u()}),()=>{for(;r.length;)r.pop()()}}var datas={};function data(e,t){datas[e]=t}function injectDataProviders(e,t){return Object.entries(datas).forEach(([n,r])=>{Object.defineProperty(e,n,{get(){return(...a)=>r.bind(t)(...a)},enumerable:!1})}),e}var Alpine={get reactive(){return reactive},get release(){return release},get effect(){return effect},get raw(){return raw},version:"3.14.9",flushAndStopDeferringMutations,dontAutoEvaluateFunctions,disableEffectScheduling,startObservingMutations,stopObservingMutations,setReactivityEngine,onAttributeRemoved,onAttributesAdded,closestDataStack,skipDuringClone,onlyDuringClone,addRootSelector,addInitSelector,interceptClone,addScopeToNode,deferMutations,mapAttributes,evaluateLater,interceptInit,setEvaluator,mergeProxies,extractProp,findClosest,onElRemoved,closestRoot,destroyTree,interceptor,transition,setStyles,mutateDom,directive,entangle,throttle,debounce,evaluate,initTree,nextTick,prefixed:prefix,prefix:setPrefix,plugin,magic,store,start,clone,cloneNode,bound:getBinding,$data:scope,watch,walk,data,bind:bind2},alpine_default=Alpine;function makeMap(e,t){const n=Object.create(null),r=e.split(",");for(let a=0;a<r.length;a++)n[r[a]]=!0;return a=>!!n[a]}var EMPTY_OBJ=Object.freeze({}),hasOwnProperty=Object.prototype.hasOwnProperty,hasOwn=(e,t)=>hasOwnProperty.call(e,t),isArray=Array.isArray,isMap=e=>toTypeString(e)==="[object Map]",isString=e=>typeof e=="string",isSymbol=e=>typeof e=="symbol",isObject=e=>e!==null&&typeof e=="object",objectToString=Object.prototype.toString,toTypeString=e=>objectToString.call(e),toRawType=e=>toTypeString(e).slice(8,-1),isIntegerKey=e=>isString(e)&&e!=="NaN"&&e[0]!=="-"&&""+parseInt(e,10)===e,cacheStringFunction=e=>{const t=Object.create(null);return n=>t[n]||(t[n]=e(n))},capitalize=cacheStringFunction(e=>e.charAt(0).toUpperCase()+e.slice(1)),hasChanged=(e,t)=>e!==t&&(e===e||t===t),targetMap=new WeakMap,effectStack=[],activeEffect,ITERATE_KEY=Symbol("iterate"),MAP_KEY_ITERATE_KEY=Symbol("Map key iterate");function isEffect(e){return e&&e._isEffect===!0}function effect2(e,t=EMPTY_OBJ){isEffect(e)&&(e=e.raw);const n=createReactiveEffect(e,t);return t.lazy||n(),n}function stop(e){e.active&&(cleanup(e),e.options.onStop&&e.options.onStop(),e.active=!1)}var uid=0;function createReactiveEffect(e,t){const n=function(){if(!n.active)return e();if(!effectStack.includes(n)){cleanup(n);try{return enableTracking(),effectStack.push(n),activeEffect=n,e()}finally{effectStack.pop(),resetTracking(),activeEffect=effectStack[effectStack.length-1]}}};return n.id=uid++,n.allowRecurse=!!t.allowRecurse,n._isEffect=!0,n.active=!0,n.raw=e,n.deps=[],n.options=t,n}function cleanup(e){const{deps:t}=e;if(t.length){for(let n=0;n<t.length;n++)t[n].delete(e);t.length=0}}var shouldTrack=!0,trackStack=[];function pauseTracking(){trackStack.push(shouldTrack),shouldTrack=!1}function enableTracking(){trackStack.push(shouldTrack),shouldTrack=!0}function resetTracking(){const e=trackStack.pop();shouldTrack=e===void 0?!0:e}function track(e,t,n){if(!shouldTrack||activeEffect===void 0)return;let r=targetMap.get(e);r||targetMap.set(e,r=new Map);let a=r.get(n);a||r.set(n,a=new Set),a.has(activeEffect)||(a.add(activeEffect),activeEffect.deps.push(a),activeEffect.options.onTrack&&activeEffect.options.onTrack({effect:activeEffect,target:e,type:t,key:n}))}function trigger(e,t,n,r,a,o){const u=targetMap.get(e);if(!u)return;const E=new Set,b=P=>{P&&P.forEach(j=>{(j!==activeEffect||j.allowRecurse)&&E.add(j)})};if(t==="clear")u.forEach(b);else if(n==="length"&&isArray(e))u.forEach((P,j)=>{(j==="length"||j>=r)&&b(P)});else switch(n!==void 0&&b(u.get(n)),t){case"add":isArray(e)?isIntegerKey(n)&&b(u.get("length")):(b(u.get(ITERATE_KEY)),isMap(e)&&b(u.get(MAP_KEY_ITERATE_KEY)));break;case"delete":isArray(e)||(b(u.get(ITERATE_KEY)),isMap(e)&&b(u.get(MAP_KEY_ITERATE_KEY)));break;case"set":isMap(e)&&b(u.get(ITERATE_KEY));break}const _=P=>{P.options.onTrigger&&P.options.onTrigger({effect:P,target:e,key:n,type:t,newValue:r,oldValue:a,oldTarget:o}),P.options.scheduler?P.options.scheduler(P):P()};E.forEach(_)}var isNonTrackableKeys=makeMap("__proto__,__v_isRef,__isVue"),builtInSymbols=new Set(Object.getOwnPropertyNames(Symbol).map(e=>Symbol[e]).filter(isSymbol)),get2=createGetter(),readonlyGet=createGetter(!0),arrayInstrumentations=createArrayInstrumentations();function createArrayInstrumentations(){const e={};return["includes","indexOf","lastIndexOf"].forEach(t=>{e[t]=function(...n){const r=toRaw(this);for(let o=0,u=this.length;o<u;o++)track(r,"get",o+"");const a=r[t](...n);return a===-1||a===!1?r[t](...n.map(toRaw)):a}}),["push","pop","shift","unshift","splice"].forEach(t=>{e[t]=function(...n){pauseTracking();const r=toRaw(this)[t].apply(this,n);return resetTracking(),r}}),e}function createGetter(e=!1,t=!1){return function(r,a,o){if(a==="__v_isReactive")return!e;if(a==="__v_isReadonly")return e;if(a==="__v_raw"&&o===(e?t?shallowReadonlyMap:readonlyMap:t?shallowReactiveMap:reactiveMap).get(r))return r;const u=isArray(r);if(!e&&u&&hasOwn(arrayInstrumentations,a))return Reflect.get(arrayInstrumentations,a,o);const E=Reflect.get(r,a,o);return(isSymbol(a)?builtInSymbols.has(a):isNonTrackableKeys(a))||(e||track(r,"get",a),t)?E:isRef(E)?!u||!isIntegerKey(a)?E.value:E:isObject(E)?e?readonly(E):reactive2(E):E}}var set2=createSetter();function createSetter(e=!1){return function(n,r,a,o){let u=n[r];if(!e&&(a=toRaw(a),u=toRaw(u),!isArray(n)&&isRef(u)&&!isRef(a)))return u.value=a,!0;const E=isArray(n)&&isIntegerKey(r)?Number(r)<n.length:hasOwn(n,r),b=Reflect.set(n,r,a,o);return n===toRaw(o)&&(E?hasChanged(a,u)&&trigger(n,"set",r,a,u):trigger(n,"add",r,a)),b}}function deleteProperty(e,t){const n=hasOwn(e,t),r=e[t],a=Reflect.deleteProperty(e,t);return a&&n&&trigger(e,"delete",t,void 0,r),a}function has(e,t){const n=Reflect.has(e,t);return(!isSymbol(t)||!builtInSymbols.has(t))&&track(e,"has",t),n}function ownKeys(e){return track(e,"iterate",isArray(e)?"length":ITERATE_KEY),Reflect.ownKeys(e)}var mutableHandlers={get:get2,set:set2,deleteProperty,has,ownKeys},readonlyHandlers={get:readonlyGet,set(e,t){return console.warn(`Set operation on key "${String(t)}" failed: target is readonly.`,e),!0},deleteProperty(e,t){return console.warn(`Delete operation on key "${String(t)}" failed: target is readonly.`,e),!0}},toReactive=e=>isObject(e)?reactive2(e):e,toReadonly=e=>isObject(e)?readonly(e):e,toShallow=e=>e,getProto=e=>Reflect.getPrototypeOf(e);function get$1(e,t,n=!1,r=!1){e=e.__v_raw;const a=toRaw(e),o=toRaw(t);t!==o&&!n&&track(a,"get",t),!n&&track(a,"get",o);const{has:u}=getProto(a),E=r?toShallow:n?toReadonly:toReactive;if(u.call(a,t))return E(e.get(t));if(u.call(a,o))return E(e.get(o));e!==a&&e.get(t)}function has$1(e,t=!1){const n=this.__v_raw,r=toRaw(n),a=toRaw(e);return e!==a&&!t&&track(r,"has",e),!t&&track(r,"has",a),e===a?n.has(e):n.has(e)||n.has(a)}function size(e,t=!1){return e=e.__v_raw,!t&&track(toRaw(e),"iterate",ITERATE_KEY),Reflect.get(e,"size",e)}function add(e){e=toRaw(e);const t=toRaw(this);return getProto(t).has.call(t,e)||(t.add(e),trigger(t,"add",e,e)),this}function set$1(e,t){t=toRaw(t);const n=toRaw(this),{has:r,get:a}=getProto(n);let o=r.call(n,e);o?checkIdentityKeys(n,r,e):(e=toRaw(e),o=r.call(n,e));const u=a.call(n,e);return n.set(e,t),o?hasChanged(t,u)&&trigger(n,"set",e,t,u):trigger(n,"add",e,t),this}function deleteEntry(e){const t=toRaw(this),{has:n,get:r}=getProto(t);let a=n.call(t,e);a?checkIdentityKeys(t,n,e):(e=toRaw(e),a=n.call(t,e));const o=r?r.call(t,e):void 0,u=t.delete(e);return a&&trigger(t,"delete",e,void 0,o),u}function clear(){const e=toRaw(this),t=e.size!==0,n=isMap(e)?new Map(e):new Set(e),r=e.clear();return t&&trigger(e,"clear",void 0,void 0,n),r}function createForEach(e,t){return function(r,a){const o=this,u=o.__v_raw,E=toRaw(u),b=t?toShallow:e?toReadonly:toReactive;return!e&&track(E,"iterate",ITERATE_KEY),u.forEach((_,P)=>r.call(a,b(_),b(P),o))}}function createIterableMethod(e,t,n){return function(...r){const a=this.__v_raw,o=toRaw(a),u=isMap(o),E=e==="entries"||e===Symbol.iterator&&u,b=e==="keys"&&u,_=a[e](...r),P=n?toShallow:t?toReadonly:toReactive;return!t&&track(o,"iterate",b?MAP_KEY_ITERATE_KEY:ITERATE_KEY),{next(){const{value:j,done:$}=_.next();return $?{value:j,done:$}:{value:E?[P(j[0]),P(j[1])]:P(j),done:$}},[Symbol.iterator](){return this}}}}function createReadonlyMethod(e){return function(...t){{const n=t[0]?`on key "${t[0]}" `:"";console.warn(`${capitalize(e)} operation ${n}failed: target is readonly.`,toRaw(this))}return e==="delete"?!1:this}}function createInstrumentations(){const e={get(o){return get$1(this,o)},get size(){return size(this)},has:has$1,add,set:set$1,delete:deleteEntry,clear,forEach:createForEach(!1,!1)},t={get(o){return get$1(this,o,!1,!0)},get size(){return size(this)},has:has$1,add,set:set$1,delete:deleteEntry,clear,forEach:createForEach(!1,!0)},n={get(o){return get$1(this,o,!0)},get size(){return size(this,!0)},has(o){return has$1.call(this,o,!0)},add:createReadonlyMethod("add"),set:createReadonlyMethod("set"),delete:createReadonlyMethod("delete"),clear:createReadonlyMethod("clear"),forEach:createForEach(!0,!1)},r={get(o){return get$1(this,o,!0,!0)},get size(){return size(this,!0)},has(o){return has$1.call(this,o,!0)},add:createReadonlyMethod("add"),set:createReadonlyMethod("set"),delete:createReadonlyMethod("delete"),clear:createReadonlyMethod("clear"),forEach:createForEach(!0,!0)};return["keys","values","entries",Symbol.iterator].forEach(o=>{e[o]=createIterableMethod(o,!1,!1),n[o]=createIterableMethod(o,!0,!1),t[o]=createIterableMethod(o,!1,!0),r[o]=createIterableMethod(o,!0,!0)}),[e,n,t,r]}var[mutableInstrumentations,readonlyInstrumentations,shallowInstrumentations,shallowReadonlyInstrumentations]=createInstrumentations();function createInstrumentationGetter(e,t){const n=e?readonlyInstrumentations:mutableInstrumentations;return(r,a,o)=>a==="__v_isReactive"?!e:a==="__v_isReadonly"?e:a==="__v_raw"?r:Reflect.get(hasOwn(n,a)&&a in r?n:r,a,o)}var mutableCollectionHandlers={get:createInstrumentationGetter(!1)},readonlyCollectionHandlers={get:createInstrumentationGetter(!0)};function checkIdentityKeys(e,t,n){const r=toRaw(n);if(r!==n&&t.call(e,r)){const a=toRawType(e);console.warn(`Reactive ${a} contains both the raw and reactive versions of the same object${a==="Map"?" as keys":""}, which can lead to inconsistencies. Avoid differentiating between the raw and reactive versions of an object and only use the reactive version if possible.`)}}var reactiveMap=new WeakMap,shallowReactiveMap=new WeakMap,readonlyMap=new WeakMap,shallowReadonlyMap=new WeakMap;function targetTypeMap(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function getTargetType(e){return e.__v_skip||!Object.isExtensible(e)?0:targetTypeMap(toRawType(e))}function reactive2(e){return e&&e.__v_isReadonly?e:createReactiveObject(e,!1,mutableHandlers,mutableCollectionHandlers,reactiveMap)}function readonly(e){return createReactiveObject(e,!0,readonlyHandlers,readonlyCollectionHandlers,readonlyMap)}function createReactiveObject(e,t,n,r,a){if(!isObject(e))return console.warn(`value cannot be made reactive: ${String(e)}`),e;if(e.__v_raw&&!(t&&e.__v_isReactive))return e;const o=a.get(e);if(o)return o;const u=getTargetType(e);if(u===0)return e;const E=new Proxy(e,u===2?r:n);return a.set(e,E),E}function toRaw(e){return e&&toRaw(e.__v_raw)||e}function isRef(e){return!!(e&&e.__v_isRef===!0)}magic("nextTick",()=>nextTick);magic("dispatch",e=>dispatch.bind(dispatch,e));magic("watch",(e,{evaluateLater:t,cleanup:n})=>(r,a)=>{let o=t(r),E=watch(()=>{let b;return o(_=>b=_),b},a);n(E)});magic("store",getStores);magic("data",e=>scope(e));magic("root",e=>closestRoot(e));magic("refs",e=>(e._x_refs_proxy||(e._x_refs_proxy=mergeProxies(getArrayOfRefObject(e))),e._x_refs_proxy));function getArrayOfRefObject(e){let t=[];return findClosest(e,n=>{n._x_refs&&t.push(n._x_refs)}),t}var globalIdMemo={};function findAndIncrementId(e){return globalIdMemo[e]||(globalIdMemo[e]=0),++globalIdMemo[e]}function closestIdRoot(e,t){return findClosest(e,n=>{if(n._x_ids&&n._x_ids[t])return!0})}function setIdRoot(e,t){e._x_ids||(e._x_ids={}),e._x_ids[t]||(e._x_ids[t]=findAndIncrementId(t))}magic("id",(e,{cleanup:t})=>(n,r=null)=>{let a=`${n}${r?`-${r}`:""}`;return cacheIdByNameOnElement(e,a,t,()=>{let o=closestIdRoot(e,n),u=o?o._x_ids[n]:findAndIncrementId(n);return r?`${n}-${u}-${r}`:`${n}-${u}`})});interceptClone((e,t)=>{e._x_id&&(t._x_id=e._x_id)});function cacheIdByNameOnElement(e,t,n,r){if(e._x_id||(e._x_id={}),e._x_id[t])return e._x_id[t];let a=r();return e._x_id[t]=a,n(()=>{delete e._x_id[t]}),a}magic("el",e=>e);warnMissingPluginMagic("Focus","focus","focus");warnMissingPluginMagic("Persist","persist","persist");function warnMissingPluginMagic(e,t,n){magic(t,r=>warn(`You can't use [$${t}] without first installing the "${e}" plugin here: https://alpinejs.dev/plugins/${n}`,r))}directive("modelable",(e,{expression:t},{effect:n,evaluateLater:r,cleanup:a})=>{let o=r(t),u=()=>{let P;return o(j=>P=j),P},E=r(`${t} = __placeholder`),b=P=>E(()=>{},{scope:{__placeholder:P}}),_=u();b(_),queueMicrotask(()=>{if(!e._x_model)return;e._x_removeModelListeners.default();let P=e._x_model.get,j=e._x_model.set,$=entangle({get(){return P()},set(J){j(J)}},{get(){return u()},set(J){b(J)}});a($)})});directive("teleport",(e,{modifiers:t,expression:n},{cleanup:r})=>{e.tagName.toLowerCase()!=="template"&&warn("x-teleport can only be used on a <template> tag",e);let a=getTarget(n),o=e.content.cloneNode(!0).firstElementChild;e._x_teleport=o,o._x_teleportBack=e,e.setAttribute("data-teleport-template",!0),o.setAttribute("data-teleport-target",!0),e._x_forwardEvents&&e._x_forwardEvents.forEach(E=>{o.addEventListener(E,b=>{b.stopPropagation(),e.dispatchEvent(new b.constructor(b.type,b))})}),addScopeToNode(o,{},e);let u=(E,b,_)=>{_.includes("prepend")?b.parentNode.insertBefore(E,b):_.includes("append")?b.parentNode.insertBefore(E,b.nextSibling):b.appendChild(E)};mutateDom(()=>{u(o,a,t),skipDuringClone(()=>{initTree(o)})()}),e._x_teleportPutBack=()=>{let E=getTarget(n);mutateDom(()=>{u(e._x_teleport,E,t)})},r(()=>mutateDom(()=>{o.remove(),destroyTree(o)}))});var teleportContainerDuringClone=document.createElement("div");function getTarget(e){let t=skipDuringClone(()=>document.querySelector(e),()=>teleportContainerDuringClone)();return t||warn(`Cannot find x-teleport element for selector: "${e}"`),t}var handler=()=>{};handler.inline=(e,{modifiers:t},{cleanup:n})=>{t.includes("self")?e._x_ignoreSelf=!0:e._x_ignore=!0,n(()=>{t.includes("self")?delete e._x_ignoreSelf:delete e._x_ignore})};directive("ignore",handler);directive("effect",skipDuringClone((e,{expression:t},{effect:n})=>{n(evaluateLater(e,t))}));function on(e,t,n,r){let a=e,o=b=>r(b),u={},E=(b,_)=>P=>_(b,P);if(n.includes("dot")&&(t=dotSyntax(t)),n.includes("camel")&&(t=camelCase2(t)),n.includes("passive")&&(u.passive=!0),n.includes("capture")&&(u.capture=!0),n.includes("window")&&(a=window),n.includes("document")&&(a=document),n.includes("debounce")){let b=n[n.indexOf("debounce")+1]||"invalid-wait",_=isNumeric(b.split("ms")[0])?Number(b.split("ms")[0]):250;o=debounce(o,_)}if(n.includes("throttle")){let b=n[n.indexOf("throttle")+1]||"invalid-wait",_=isNumeric(b.split("ms")[0])?Number(b.split("ms")[0]):250;o=throttle(o,_)}return n.includes("prevent")&&(o=E(o,(b,_)=>{_.preventDefault(),b(_)})),n.includes("stop")&&(o=E(o,(b,_)=>{_.stopPropagation(),b(_)})),n.includes("once")&&(o=E(o,(b,_)=>{b(_),a.removeEventListener(t,o,u)})),(n.includes("away")||n.includes("outside"))&&(a=document,o=E(o,(b,_)=>{e.contains(_.target)||_.target.isConnected!==!1&&(e.offsetWidth<1&&e.offsetHeight<1||e._x_isShown!==!1&&b(_))})),n.includes("self")&&(o=E(o,(b,_)=>{_.target===e&&b(_)})),(isKeyEvent(t)||isClickEvent(t))&&(o=E(o,(b,_)=>{isListeningForASpecificKeyThatHasntBeenPressed(_,n)||b(_)})),a.addEventListener(t,o,u),()=>{a.removeEventListener(t,o,u)}}function dotSyntax(e){return e.replace(/-/g,".")}function camelCase2(e){return e.toLowerCase().replace(/-(\w)/g,(t,n)=>n.toUpperCase())}function isNumeric(e){return!Array.isArray(e)&&!isNaN(e)}function kebabCase2(e){return[" ","_"].includes(e)?e:e.replace(/([a-z])([A-Z])/g,"$1-$2").replace(/[_\s]/,"-").toLowerCase()}function isKeyEvent(e){return["keydown","keyup"].includes(e)}function isClickEvent(e){return["contextmenu","click","mouse"].some(t=>e.includes(t))}function isListeningForASpecificKeyThatHasntBeenPressed(e,t){let n=t.filter(o=>!["window","document","prevent","stop","once","capture","self","away","outside","passive"].includes(o));if(n.includes("debounce")){let o=n.indexOf("debounce");n.splice(o,isNumeric((n[o+1]||"invalid-wait").split("ms")[0])?2:1)}if(n.includes("throttle")){let o=n.indexOf("throttle");n.splice(o,isNumeric((n[o+1]||"invalid-wait").split("ms")[0])?2:1)}if(n.length===0||n.length===1&&keyToModifiers(e.key).includes(n[0]))return!1;const a=["ctrl","shift","alt","meta","cmd","super"].filter(o=>n.includes(o));return n=n.filter(o=>!a.includes(o)),!(a.length>0&&a.filter(u=>((u==="cmd"||u==="super")&&(u="meta"),e[`${u}Key`])).length===a.length&&(isClickEvent(e.type)||keyToModifiers(e.key).includes(n[0])))}function keyToModifiers(e){if(!e)return[];e=kebabCase2(e);let t={ctrl:"control",slash:"/",space:" ",spacebar:" ",cmd:"meta",esc:"escape",up:"arrow-up",down:"arrow-down",left:"arrow-left",right:"arrow-right",period:".",comma:",",equal:"=",minus:"-",underscore:"_"};return t[e]=e,Object.keys(t).map(n=>{if(t[n]===e)return n}).filter(n=>n)}directive("model",(e,{modifiers:t,expression:n},{effect:r,cleanup:a})=>{let o=e;t.includes("parent")&&(o=e.parentNode);let u=evaluateLater(o,n),E;typeof n=="string"?E=evaluateLater(o,`${n} = __placeholder`):typeof n=="function"&&typeof n()=="string"?E=evaluateLater(o,`${n()} = __placeholder`):E=()=>{};let b=()=>{let $;return u(J=>$=J),isGetterSetter($)?$.get():$},_=$=>{let J;u(re=>J=re),isGetterSetter(J)?J.set($):E(()=>{},{scope:{__placeholder:$}})};typeof n=="string"&&e.type==="radio"&&mutateDom(()=>{e.hasAttribute("name")||e.setAttribute("name",n)});var P=e.tagName.toLowerCase()==="select"||["checkbox","radio"].includes(e.type)||t.includes("lazy")?"change":"input";let j=isCloning?()=>{}:on(e,P,t,$=>{_(getInputValue(e,t,$,b()))});if(t.includes("fill")&&([void 0,null,""].includes(b())||isCheckbox(e)&&Array.isArray(b())||e.tagName.toLowerCase()==="select"&&e.multiple)&&_(getInputValue(e,t,{target:e},b())),e._x_removeModelListeners||(e._x_removeModelListeners={}),e._x_removeModelListeners.default=j,a(()=>e._x_removeModelListeners.default()),e.form){let $=on(e.form,"reset",[],J=>{nextTick(()=>e._x_model&&e._x_model.set(getInputValue(e,t,{target:e},b())))});a(()=>$())}e._x_model={get(){return b()},set($){_($)}},e._x_forceModelUpdate=$=>{$===void 0&&typeof n=="string"&&n.match(/\./)&&($=""),window.fromModel=!0,mutateDom(()=>bind(e,"value",$)),delete window.fromModel},r(()=>{let $=b();t.includes("unintrusive")&&document.activeElement.isSameNode(e)||e._x_forceModelUpdate($)})});function getInputValue(e,t,n,r){return mutateDom(()=>{if(n instanceof CustomEvent&&n.detail!==void 0)return n.detail!==null&&n.detail!==void 0?n.detail:n.target.value;if(isCheckbox(e))if(Array.isArray(r)){let a=null;return t.includes("number")?a=safeParseNumber(n.target.value):t.includes("boolean")?a=safeParseBoolean(n.target.value):a=n.target.value,n.target.checked?r.includes(a)?r:r.concat([a]):r.filter(o=>!checkedAttrLooseCompare2(o,a))}else return n.target.checked;else{if(e.tagName.toLowerCase()==="select"&&e.multiple)return t.includes("number")?Array.from(n.target.selectedOptions).map(a=>{let o=a.value||a.text;return safeParseNumber(o)}):t.includes("boolean")?Array.from(n.target.selectedOptions).map(a=>{let o=a.value||a.text;return safeParseBoolean(o)}):Array.from(n.target.selectedOptions).map(a=>a.value||a.text);{let a;return isRadio(e)?n.target.checked?a=n.target.value:a=r:a=n.target.value,t.includes("number")?safeParseNumber(a):t.includes("boolean")?safeParseBoolean(a):t.includes("trim")?a.trim():a}}})}function safeParseNumber(e){let t=e?parseFloat(e):null;return isNumeric2(t)?t:e}function checkedAttrLooseCompare2(e,t){return e==t}function isNumeric2(e){return!Array.isArray(e)&&!isNaN(e)}function isGetterSetter(e){return e!==null&&typeof e=="object"&&typeof e.get=="function"&&typeof e.set=="function"}directive("cloak",e=>queueMicrotask(()=>mutateDom(()=>e.removeAttribute(prefix("cloak")))));addInitSelector(()=>`[${prefix("init")}]`);directive("init",skipDuringClone((e,{expression:t},{evaluate:n})=>typeof t=="string"?!!t.trim()&&n(t,{},!1):n(t,{},!1)));directive("text",(e,{expression:t},{effect:n,evaluateLater:r})=>{let a=r(t);n(()=>{a(o=>{mutateDom(()=>{e.textContent=o})})})});directive("html",(e,{expression:t},{effect:n,evaluateLater:r})=>{let a=r(t);n(()=>{a(o=>{mutateDom(()=>{e.innerHTML=o,e._x_ignoreSelf=!0,initTree(e),delete e._x_ignoreSelf})})})});mapAttributes(startingWith(":",into(prefix("bind:"))));var handler2=(e,{value:t,modifiers:n,expression:r,original:a},{effect:o,cleanup:u})=>{if(!t){let b={};injectBindingProviders(b),evaluateLater(e,r)(P=>{applyBindingsObject(e,P,a)},{scope:b});return}if(t==="key")return storeKeyForXFor(e,r);if(e._x_inlineBindings&&e._x_inlineBindings[t]&&e._x_inlineBindings[t].extract)return;let E=evaluateLater(e,r);o(()=>E(b=>{b===void 0&&typeof r=="string"&&r.match(/\./)&&(b=""),mutateDom(()=>bind(e,t,b,n))})),u(()=>{e._x_undoAddedClasses&&e._x_undoAddedClasses(),e._x_undoAddedStyles&&e._x_undoAddedStyles()})};handler2.inline=(e,{value:t,modifiers:n,expression:r})=>{t&&(e._x_inlineBindings||(e._x_inlineBindings={}),e._x_inlineBindings[t]={expression:r,extract:!1})};directive("bind",handler2);function storeKeyForXFor(e,t){e._x_keyExpression=t}addRootSelector(()=>`[${prefix("data")}]`);directive("data",(e,{expression:t},{cleanup:n})=>{if(shouldSkipRegisteringDataDuringClone(e))return;t=t===""?"{}":t;let r={};injectMagics(r,e);let a={};injectDataProviders(a,r);let o=evaluate(e,t,{scope:a});(o===void 0||o===!0)&&(o={}),injectMagics(o,e);let u=reactive(o);initInterceptors(u);let E=addScopeToNode(e,u);u.init&&evaluate(e,u.init),n(()=>{u.destroy&&evaluate(e,u.destroy),E()})});interceptClone((e,t)=>{e._x_dataStack&&(t._x_dataStack=e._x_dataStack,t.setAttribute("data-has-alpine-state",!0))});function shouldSkipRegisteringDataDuringClone(e){return isCloning?isCloningLegacy?!0:e.hasAttribute("data-has-alpine-state"):!1}directive("show",(e,{modifiers:t,expression:n},{effect:r})=>{let a=evaluateLater(e,n);e._x_doHide||(e._x_doHide=()=>{mutateDom(()=>{e.style.setProperty("display","none",t.includes("important")?"important":void 0)})}),e._x_doShow||(e._x_doShow=()=>{mutateDom(()=>{e.style.length===1&&e.style.display==="none"?e.removeAttribute("style"):e.style.removeProperty("display")})});let o=()=>{e._x_doHide(),e._x_isShown=!1},u=()=>{e._x_doShow(),e._x_isShown=!0},E=()=>setTimeout(u),b=once(j=>j?u():o(),j=>{typeof e._x_toggleAndCascadeWithTransitions=="function"?e._x_toggleAndCascadeWithTransitions(e,j,u,o):j?E():o()}),_,P=!0;r(()=>a(j=>{!P&&j===_||(t.includes("immediate")&&(j?E():o()),b(j),_=j,P=!1)}))});directive("for",(e,{expression:t},{effect:n,cleanup:r})=>{let a=parseForExpression(t),o=evaluateLater(e,a.items),u=evaluateLater(e,e._x_keyExpression||"index");e._x_prevKeys=[],e._x_lookup={},n(()=>loop(e,a,o,u)),r(()=>{Object.values(e._x_lookup).forEach(E=>mutateDom(()=>{destroyTree(E),E.remove()})),delete e._x_prevKeys,delete e._x_lookup})});function loop(e,t,n,r){let a=u=>typeof u=="object"&&!Array.isArray(u),o=e;n(u=>{isNumeric3(u)&&u>=0&&(u=Array.from(Array(u).keys(),W=>W+1)),u===void 0&&(u=[]);let E=e._x_lookup,b=e._x_prevKeys,_=[],P=[];if(a(u))u=Object.entries(u).map(([W,Q])=>{let te=getIterationScopeVariables(t,Q,W,u);r(ee=>{P.includes(ee)&&warn("Duplicate key on x-for",e),P.push(ee)},{scope:{index:W,...te}}),_.push(te)});else for(let W=0;W<u.length;W++){let Q=getIterationScopeVariables(t,u[W],W,u);r(te=>{P.includes(te)&&warn("Duplicate key on x-for",e),P.push(te)},{scope:{index:W,...Q}}),_.push(Q)}let j=[],$=[],J=[],re=[];for(let W=0;W<b.length;W++){let Q=b[W];P.indexOf(Q)===-1&&J.push(Q)}b=b.filter(W=>!J.includes(W));let ce="template";for(let W=0;W<P.length;W++){let Q=P[W],te=b.indexOf(Q);if(te===-1)b.splice(W,0,Q),j.push([ce,W]);else if(te!==W){let ee=b.splice(W,1)[0],se=b.splice(te-1,1)[0];b.splice(W,0,se),b.splice(te,0,ee),$.push([ee,se])}else re.push(Q);ce=Q}for(let W=0;W<J.length;W++){let Q=J[W];Q in E&&(mutateDom(()=>{destroyTree(E[Q]),E[Q].remove()}),delete E[Q])}for(let W=0;W<$.length;W++){let[Q,te]=$[W],ee=E[Q],se=E[te],ge=document.createElement("div");mutateDom(()=>{se||warn('x-for ":key" is undefined or invalid',o,te,E),se.after(ge),ee.after(se),se._x_currentIfEl&&se.after(se._x_currentIfEl),ge.before(ee),ee._x_currentIfEl&&ee.after(ee._x_currentIfEl),ge.remove()}),se._x_refreshXForScope(_[P.indexOf(te)])}for(let W=0;W<j.length;W++){let[Q,te]=j[W],ee=Q==="template"?o:E[Q];ee._x_currentIfEl&&(ee=ee._x_currentIfEl);let se=_[te],ge=P[te],oe=document.importNode(o.content,!0).firstElementChild,me=reactive(se);addScopeToNode(oe,me,o),oe._x_refreshXForScope=le=>{Object.entries(le).forEach(([Se,Ae])=>{me[Se]=Ae})},mutateDom(()=>{ee.after(oe),skipDuringClone(()=>initTree(oe))()}),typeof ge=="object"&&warn("x-for key cannot be an object, it must be a string or an integer",o),E[ge]=oe}for(let W=0;W<re.length;W++)E[re[W]]._x_refreshXForScope(_[P.indexOf(re[W])]);o._x_prevKeys=P})}function parseForExpression(e){let t=/,([^,\}\]]*)(?:,([^,\}\]]*))?$/,n=/^\s*\(|\)\s*$/g,r=/([\s\S]*?)\s+(?:in|of)\s+([\s\S]*)/,a=e.match(r);if(!a)return;let o={};o.items=a[2].trim();let u=a[1].replace(n,"").trim(),E=u.match(t);return E?(o.item=u.replace(t,"").trim(),o.index=E[1].trim(),E[2]&&(o.collection=E[2].trim())):o.item=u,o}function getIterationScopeVariables(e,t,n,r){let a={};return/^\[.*\]$/.test(e.item)&&Array.isArray(t)?e.item.replace("[","").replace("]","").split(",").map(u=>u.trim()).forEach((u,E)=>{a[u]=t[E]}):/^\{.*\}$/.test(e.item)&&!Array.isArray(t)&&typeof t=="object"?e.item.replace("{","").replace("}","").split(",").map(u=>u.trim()).forEach(u=>{a[u]=t[u]}):a[e.item]=t,e.index&&(a[e.index]=n),e.collection&&(a[e.collection]=r),a}function isNumeric3(e){return!Array.isArray(e)&&!isNaN(e)}function handler3(){}handler3.inline=(e,{expression:t},{cleanup:n})=>{let r=closestRoot(e);r._x_refs||(r._x_refs={}),r._x_refs[t]=e,n(()=>delete r._x_refs[t])};directive("ref",handler3);directive("if",(e,{expression:t},{effect:n,cleanup:r})=>{e.tagName.toLowerCase()!=="template"&&warn("x-if can only be used on a <template> tag",e);let a=evaluateLater(e,t),o=()=>{if(e._x_currentIfEl)return e._x_currentIfEl;let E=e.content.cloneNode(!0).firstElementChild;return addScopeToNode(E,{},e),mutateDom(()=>{e.after(E),skipDuringClone(()=>initTree(E))()}),e._x_currentIfEl=E,e._x_undoIf=()=>{mutateDom(()=>{destroyTree(E),E.remove()}),delete e._x_currentIfEl},E},u=()=>{e._x_undoIf&&(e._x_undoIf(),delete e._x_undoIf)};n(()=>a(E=>{E?o():u()})),r(()=>e._x_undoIf&&e._x_undoIf())});directive("id",(e,{expression:t},{evaluate:n})=>{n(t).forEach(a=>setIdRoot(e,a))});interceptClone((e,t)=>{e._x_ids&&(t._x_ids=e._x_ids)});mapAttributes(startingWith("@",into(prefix("on:"))));directive("on",skipDuringClone((e,{value:t,modifiers:n,expression:r},{cleanup:a})=>{let o=r?evaluateLater(e,r):()=>{};e.tagName.toLowerCase()==="template"&&(e._x_forwardEvents||(e._x_forwardEvents=[]),e._x_forwardEvents.includes(t)||e._x_forwardEvents.push(t));let u=on(e,t,n,E=>{o(()=>{},{scope:{$event:E},params:[E]})});a(()=>u())}));warnMissingPluginDirective("Collapse","collapse","collapse");warnMissingPluginDirective("Intersect","intersect","intersect");warnMissingPluginDirective("Focus","trap","focus");warnMissingPluginDirective("Mask","mask","mask");function warnMissingPluginDirective(e,t,n){directive(t,r=>warn(`You can't use [x-${t}] without first installing the "${e}" plugin here: https://alpinejs.dev/plugins/${n}`,r))}alpine_default.setEvaluator(normalEvaluator);alpine_default.setReactivityEngine({reactive:reactive2,effect:effect2,release:stop,raw:toRaw});var src_default=alpine_default,module_default=src_default,htmx=function(){const htmx={onLoad:null,process:null,on:null,off:null,trigger:null,ajax:null,find:null,findAll:null,closest:null,values:function(e,t){return getInputValues(e,t||"post").values},remove:null,addClass:null,removeClass:null,toggleClass:null,takeClass:null,swap:null,defineExtension:null,removeExtension:null,logAll:null,logNone:null,logger:null,config:{historyEnabled:!0,historyCacheSize:10,refreshOnHistoryMiss:!1,defaultSwapStyle:"innerHTML",defaultSwapDelay:0,defaultSettleDelay:20,includeIndicatorStyles:!0,indicatorClass:"htmx-indicator",requestClass:"htmx-request",addedClass:"htmx-added",settlingClass:"htmx-settling",swappingClass:"htmx-swapping",allowEval:!0,allowScriptTags:!0,inlineScriptNonce:"",inlineStyleNonce:"",attributesToSettle:["class","style","width","height"],withCredentials:!1,timeout:0,wsReconnectDelay:"full-jitter",wsBinaryType:"blob",disableSelector:"[hx-disable], [data-hx-disable]",scrollBehavior:"instant",defaultFocusScroll:!1,getCacheBusterParam:!1,globalViewTransitions:!1,methodsThatUseUrlParams:["get","delete"],selfRequestsOnly:!0,ignoreTitle:!1,scrollIntoViewOnBoost:!0,triggerSpecsCache:null,disableInheritance:!1,responseHandling:[{code:"204",swap:!1},{code:"[23]..",swap:!0},{code:"[45]..",swap:!1,error:!0}],allowNestedOobSwaps:!0},parseInterval:null,_:null,version:"2.0.4"};htmx.onLoad=onLoadHelper,htmx.process=processNode,htmx.on=addEventListenerImpl,htmx.off=removeEventListenerImpl,htmx.trigger=triggerEvent,htmx.ajax=ajaxHelper,htmx.find=find,htmx.findAll=findAll,htmx.closest=closest,htmx.remove=removeElement,htmx.addClass=addClassToElement,htmx.removeClass=removeClassFromElement,htmx.toggleClass=toggleClassOnElement,htmx.takeClass=takeClassForElement,htmx.swap=swap,htmx.defineExtension=defineExtension,htmx.removeExtension=removeExtension,htmx.logAll=logAll,htmx.logNone=logNone,htmx.parseInterval=parseInterval,htmx._=internalEval;const internalAPI={addTriggerHandler,bodyContains,canAccessLocalStorage,findThisElement,filterValues,swap,hasAttribute,getAttributeValue,getClosestAttributeValue,getClosestMatch,getExpressionVars,getHeaders,getInputValues,getInternalData,getSwapSpecification,getTriggerSpecs,getTarget,makeFragment,mergeObjects,makeSettleInfo,oobSwap,querySelectorExt,settleImmediately,shouldCancel,triggerEvent,triggerErrorEvent,withExtensions},VERBS=["get","post","put","delete","patch"],VERB_SELECTOR=VERBS.map(function(e){return"[hx-"+e+"], [data-hx-"+e+"]"}).join(", ");function parseInterval(e){if(e==null)return;let t=NaN;return e.slice(-2)=="ms"?t=parseFloat(e.slice(0,-2)):e.slice(-1)=="s"?t=parseFloat(e.slice(0,-1))*1e3:e.slice(-1)=="m"?t=parseFloat(e.slice(0,-1))*1e3*60:t=parseFloat(e),isNaN(t)?void 0:t}function getRawAttribute(e,t){return e instanceof Element&&e.getAttribute(t)}function hasAttribute(e,t){return!!e.hasAttribute&&(e.hasAttribute(t)||e.hasAttribute("data-"+t))}function getAttributeValue(e,t){return getRawAttribute(e,t)||getRawAttribute(e,"data-"+t)}function parentElt(e){const t=e.parentElement;return!t&&e.parentNode instanceof ShadowRoot?e.parentNode:t}function getDocument(){return document}function getRootNode(e,t){return e.getRootNode?e.getRootNode({composed:t}):getDocument()}function getClosestMatch(e,t){for(;e&&!t(e);)e=parentElt(e);return e||null}function getAttributeValueWithDisinheritance(e,t,n){const r=getAttributeValue(t,n),a=getAttributeValue(t,"hx-disinherit");var o=getAttributeValue(t,"hx-inherit");if(e!==t){if(htmx.config.disableInheritance)return o&&(o==="*"||o.split(" ").indexOf(n)>=0)?r:null;if(a&&(a==="*"||a.split(" ").indexOf(n)>=0))return"unset"}return r}function getClosestAttributeValue(e,t){let n=null;if(getClosestMatch(e,function(r){return!!(n=getAttributeValueWithDisinheritance(e,asElement(r),t))}),n!=="unset")return n}function matches(e,t){const n=e instanceof Element&&(e.matches||e.matchesSelector||e.msMatchesSelector||e.mozMatchesSelector||e.webkitMatchesSelector||e.oMatchesSelector);return!!n&&n.call(e,t)}function getStartTag(e){const n=/<([a-z][^\/\0>\x20\t\r\n\f]*)/i.exec(e);return n?n[1].toLowerCase():""}function parseHTML(e){return new DOMParser().parseFromString(e,"text/html")}function takeChildrenFor(e,t){for(;t.childNodes.length>0;)e.append(t.childNodes[0])}function duplicateScript(e){const t=getDocument().createElement("script");return forEach(e.attributes,function(n){t.setAttribute(n.name,n.value)}),t.textContent=e.textContent,t.async=!1,htmx.config.inlineScriptNonce&&(t.nonce=htmx.config.inlineScriptNonce),t}function isJavaScriptScriptNode(e){return e.matches("script")&&(e.type==="text/javascript"||e.type==="module"||e.type==="")}function normalizeScriptTags(e){Array.from(e.querySelectorAll("script")).forEach(t=>{if(isJavaScriptScriptNode(t)){const n=duplicateScript(t),r=t.parentNode;try{r.insertBefore(n,t)}catch(a){logError(a)}finally{t.remove()}}})}function makeFragment(e){const t=e.replace(/<head(\s[^>]*)?>[\s\S]*?<\/head>/i,""),n=getStartTag(t);let r;if(n==="html"){r=new DocumentFragment;const o=parseHTML(e);takeChildrenFor(r,o.body),r.title=o.title}else if(n==="body"){r=new DocumentFragment;const o=parseHTML(t);takeChildrenFor(r,o.body),r.title=o.title}else{const o=parseHTML('<body><template class="internal-htmx-wrapper">'+t+"</template></body>");r=o.querySelector("template").content,r.title=o.title;var a=r.querySelector("title");a&&a.parentNode===r&&(a.remove(),r.title=a.innerText)}return r&&(htmx.config.allowScriptTags?normalizeScriptTags(r):r.querySelectorAll("script").forEach(o=>o.remove())),r}function maybeCall(e){e&&e()}function isType(e,t){return Object.prototype.toString.call(e)==="[object "+t+"]"}function isFunction(e){return typeof e=="function"}function isRawObject(e){return isType(e,"Object")}function getInternalData(e){const t="htmx-internal-data";let n=e[t];return n||(n=e[t]={}),n}function toArray(e){const t=[];if(e)for(let n=0;n<e.length;n++)t.push(e[n]);return t}function forEach(e,t){if(e)for(let n=0;n<e.length;n++)t(e[n])}function isScrolledIntoView(e){const t=e.getBoundingClientRect(),n=t.top,r=t.bottom;return n<window.innerHeight&&r>=0}function bodyContains(e){return e.getRootNode({composed:!0})===document}function splitOnWhitespace(e){return e.trim().split(/\s+/)}function mergeObjects(e,t){for(const n in t)t.hasOwnProperty(n)&&(e[n]=t[n]);return e}function parseJSON(e){try{return JSON.parse(e)}catch(t){return logError(t),null}}function canAccessLocalStorage(){const e="htmx:localStorageTest";try{return localStorage.setItem(e,e),localStorage.removeItem(e),!0}catch{return!1}}function normalizePath(e){try{const t=new URL(e);return t&&(e=t.pathname+t.search),/^\/$/.test(e)||(e=e.replace(/\/+$/,"")),e}catch{return e}}function internalEval(str){return maybeEval(getDocument().body,function(){return eval(str)})}function onLoadHelper(e){return htmx.on("htmx:load",function(n){e(n.detail.elt)})}function logAll(){htmx.logger=function(e,t,n){console&&console.log(t,e,n)}}function logNone(){htmx.logger=null}function find(e,t){return typeof e!="string"?e.querySelector(t):find(getDocument(),e)}function findAll(e,t){return typeof e!="string"?e.querySelectorAll(t):findAll(getDocument(),e)}function getWindow(){return window}function removeElement(e,t){e=resolveTarget(e),t?getWindow().setTimeout(function(){removeElement(e),e=null},t):parentElt(e).removeChild(e)}function asElement(e){return e instanceof Element?e:null}function asHtmlElement(e){return e instanceof HTMLElement?e:null}function asString(e){return typeof e=="string"?e:null}function asParentNode(e){return e instanceof Element||e instanceof Document||e instanceof DocumentFragment?e:null}function addClassToElement(e,t,n){e=asElement(resolveTarget(e)),e&&(n?getWindow().setTimeout(function(){addClassToElement(e,t),e=null},n):e.classList&&e.classList.add(t))}function removeClassFromElement(e,t,n){let r=asElement(resolveTarget(e));r&&(n?getWindow().setTimeout(function(){removeClassFromElement(r,t),r=null},n):r.classList&&(r.classList.remove(t),r.classList.length===0&&r.removeAttribute("class")))}function toggleClassOnElement(e,t){e=resolveTarget(e),e.classList.toggle(t)}function takeClassForElement(e,t){e=resolveTarget(e),forEach(e.parentElement.children,function(n){removeClassFromElement(n,t)}),addClassToElement(asElement(e),t)}function closest(e,t){if(e=asElement(resolveTarget(e)),e&&e.closest)return e.closest(t);do if(e==null||matches(e,t))return e;while(e=e&&asElement(parentElt(e)));return null}function startsWith(e,t){return e.substring(0,t.length)===t}function endsWith(e,t){return e.substring(e.length-t.length)===t}function normalizeSelector(e){const t=e.trim();return startsWith(t,"<")&&endsWith(t,"/>")?t.substring(1,t.length-2):t}function querySelectorAllExt(e,t,n){if(t.indexOf("global ")===0)return querySelectorAllExt(e,t.slice(7),!0);e=resolveTarget(e);const r=[];{let u=0,E=0;for(let b=0;b<t.length;b++){const _=t[b];if(_===","&&u===0){r.push(t.substring(E,b)),E=b+1;continue}_==="<"?u++:_==="/"&&b<t.length-1&&t[b+1]===">"&&u--}E<t.length&&r.push(t.substring(E))}const a=[],o=[];for(;r.length>0;){const u=normalizeSelector(r.shift());let E;u.indexOf("closest ")===0?E=closest(asElement(e),normalizeSelector(u.substr(8))):u.indexOf("find ")===0?E=find(asParentNode(e),normalizeSelector(u.substr(5))):u==="next"||u==="nextElementSibling"?E=asElement(e).nextElementSibling:u.indexOf("next ")===0?E=scanForwardQuery(e,normalizeSelector(u.substr(5)),!!n):u==="previous"||u==="previousElementSibling"?E=asElement(e).previousElementSibling:u.indexOf("previous ")===0?E=scanBackwardsQuery(e,normalizeSelector(u.substr(9)),!!n):u==="document"?E=document:u==="window"?E=window:u==="body"?E=document.body:u==="root"?E=getRootNode(e,!!n):u==="host"?E=e.getRootNode().host:o.push(u),E&&a.push(E)}if(o.length>0){const u=o.join(","),E=asParentNode(getRootNode(e,!!n));a.push(...toArray(E.querySelectorAll(u)))}return a}var scanForwardQuery=function(e,t,n){const r=asParentNode(getRootNode(e,n)).querySelectorAll(t);for(let a=0;a<r.length;a++){const o=r[a];if(o.compareDocumentPosition(e)===Node.DOCUMENT_POSITION_PRECEDING)return o}},scanBackwardsQuery=function(e,t,n){const r=asParentNode(getRootNode(e,n)).querySelectorAll(t);for(let a=r.length-1;a>=0;a--){const o=r[a];if(o.compareDocumentPosition(e)===Node.DOCUMENT_POSITION_FOLLOWING)return o}};function querySelectorExt(e,t){return typeof e!="string"?querySelectorAllExt(e,t)[0]:querySelectorAllExt(getDocument().body,e)[0]}function resolveTarget(e,t){return typeof e=="string"?find(asParentNode(t)||document,e):e}function processEventArgs(e,t,n,r){return isFunction(t)?{target:getDocument().body,event:asString(e),listener:t,options:n}:{target:resolveTarget(e),event:asString(t),listener:n,options:r}}function addEventListenerImpl(e,t,n,r){return ready(function(){const o=processEventArgs(e,t,n,r);o.target.addEventListener(o.event,o.listener,o.options)}),isFunction(t)?t:n}function removeEventListenerImpl(e,t,n){return ready(function(){const r=processEventArgs(e,t,n);r.target.removeEventListener(r.event,r.listener)}),isFunction(t)?t:n}const DUMMY_ELT=getDocument().createElement("output");function findAttributeTargets(e,t){const n=getClosestAttributeValue(e,t);if(n){if(n==="this")return[findThisElement(e,t)];{const r=querySelectorAllExt(e,n);return r.length===0?(logError('The selector "'+n+'" on '+t+" returned no matches!"),[DUMMY_ELT]):r}}}function findThisElement(e,t){return asElement(getClosestMatch(e,function(n){return getAttributeValue(asElement(n),t)!=null}))}function getTarget(e){const t=getClosestAttributeValue(e,"hx-target");return t?t==="this"?findThisElement(e,"hx-target"):querySelectorExt(e,t):getInternalData(e).boosted?getDocument().body:e}function shouldSettleAttribute(e){const t=htmx.config.attributesToSettle;for(let n=0;n<t.length;n++)if(e===t[n])return!0;return!1}function cloneAttributes(e,t){forEach(e.attributes,function(n){!t.hasAttribute(n.name)&&shouldSettleAttribute(n.name)&&e.removeAttribute(n.name)}),forEach(t.attributes,function(n){shouldSettleAttribute(n.name)&&e.setAttribute(n.name,n.value)})}function isInlineSwap(e,t){const n=getExtensions(t);for(let r=0;r<n.length;r++){const a=n[r];try{if(a.isInlineSwap(e))return!0}catch(o){logError(o)}}return e==="outerHTML"}function oobSwap(e,t,n,r){r=r||getDocument();let a="#"+getRawAttribute(t,"id"),o="outerHTML";e==="true"||(e.indexOf(":")>0?(o=e.substring(0,e.indexOf(":")),a=e.substring(e.indexOf(":")+1)):o=e),t.removeAttribute("hx-swap-oob"),t.removeAttribute("data-hx-swap-oob");const u=querySelectorAllExt(r,a,!1);return u?(forEach(u,function(E){let b;const _=t.cloneNode(!0);b=getDocument().createDocumentFragment(),b.appendChild(_),isInlineSwap(o,E)||(b=asParentNode(_));const P={shouldSwap:!0,target:E,fragment:b};triggerEvent(E,"htmx:oobBeforeSwap",P)&&(E=P.target,P.shouldSwap&&(handlePreservedElements(b),swapWithStyle(o,E,E,b,n),restorePreservedElements()),forEach(n.elts,function(j){triggerEvent(j,"htmx:oobAfterSwap",P)}))}),t.parentNode.removeChild(t)):(t.parentNode.removeChild(t),triggerErrorEvent(getDocument().body,"htmx:oobErrorNoTarget",{content:t})),e}function restorePreservedElements(){const e=find("#--htmx-preserve-pantry--");if(e){for(const t of[...e.children]){const n=find("#"+t.id);n.parentNode.moveBefore(t,n),n.remove()}e.remove()}}function handlePreservedElements(e){forEach(findAll(e,"[hx-preserve], [data-hx-preserve]"),function(t){const n=getAttributeValue(t,"id"),r=getDocument().getElementById(n);if(r!=null)if(t.moveBefore){let a=find("#--htmx-preserve-pantry--");a==null&&(getDocument().body.insertAdjacentHTML("afterend","<div id='--htmx-preserve-pantry--'></div>"),a=find("#--htmx-preserve-pantry--")),a.moveBefore(r,null)}else t.parentNode.replaceChild(r,t)})}function handleAttributes(e,t,n){forEach(t.querySelectorAll("[id]"),function(r){const a=getRawAttribute(r,"id");if(a&&a.length>0){const o=a.replace("'","\\'"),u=r.tagName.replace(":","\\:"),E=asParentNode(e),b=E&&E.querySelector(u+"[id='"+o+"']");if(b&&b!==E){const _=r.cloneNode();cloneAttributes(r,b),n.tasks.push(function(){cloneAttributes(r,_)})}}})}function makeAjaxLoadTask(e){return function(){removeClassFromElement(e,htmx.config.addedClass),processNode(asElement(e)),processFocus(asParentNode(e)),triggerEvent(e,"htmx:load")}}function processFocus(e){const t="[autofocus]",n=asHtmlElement(matches(e,t)?e:e.querySelector(t));n!=null&&n.focus()}function insertNodesBefore(e,t,n,r){for(handleAttributes(e,n,r);n.childNodes.length>0;){const a=n.firstChild;addClassToElement(asElement(a),htmx.config.addedClass),e.insertBefore(a,t),a.nodeType!==Node.TEXT_NODE&&a.nodeType!==Node.COMMENT_NODE&&r.tasks.push(makeAjaxLoadTask(a))}}function stringHash(e,t){let n=0;for(;n<e.length;)t=(t<<5)-t+e.charCodeAt(n++)|0;return t}function attributeHash(e){let t=0;if(e.attributes)for(let n=0;n<e.attributes.length;n++){const r=e.attributes[n];r.value&&(t=stringHash(r.name,t),t=stringHash(r.value,t))}return t}function deInitOnHandlers(e){const t=getInternalData(e);if(t.onHandlers){for(let n=0;n<t.onHandlers.length;n++){const r=t.onHandlers[n];removeEventListenerImpl(e,r.event,r.listener)}delete t.onHandlers}}function deInitNode(e){const t=getInternalData(e);t.timeout&&clearTimeout(t.timeout),t.listenerInfos&&forEach(t.listenerInfos,function(n){n.on&&removeEventListenerImpl(n.on,n.trigger,n.listener)}),deInitOnHandlers(e),forEach(Object.keys(t),function(n){n!=="firstInitCompleted"&&delete t[n]})}function cleanUpElement(e){triggerEvent(e,"htmx:beforeCleanupElement"),deInitNode(e),e.children&&forEach(e.children,function(t){cleanUpElement(t)})}function swapOuterHTML(e,t,n){if(e instanceof Element&&e.tagName==="BODY")return swapInnerHTML(e,t,n);let r;const a=e.previousSibling,o=parentElt(e);if(o){for(insertNodesBefore(o,e,t,n),a==null?r=o.firstChild:r=a.nextSibling,n.elts=n.elts.filter(function(u){return u!==e});r&&r!==e;)r instanceof Element&&n.elts.push(r),r=r.nextSibling;cleanUpElement(e),e instanceof Element?e.remove():e.parentNode.removeChild(e)}}function swapAfterBegin(e,t,n){return insertNodesBefore(e,e.firstChild,t,n)}function swapBeforeBegin(e,t,n){return insertNodesBefore(parentElt(e),e,t,n)}function swapBeforeEnd(e,t,n){return insertNodesBefore(e,null,t,n)}function swapAfterEnd(e,t,n){return insertNodesBefore(parentElt(e),e.nextSibling,t,n)}function swapDelete(e){cleanUpElement(e);const t=parentElt(e);if(t)return t.removeChild(e)}function swapInnerHTML(e,t,n){const r=e.firstChild;if(insertNodesBefore(e,r,t,n),r){for(;r.nextSibling;)cleanUpElement(r.nextSibling),e.removeChild(r.nextSibling);cleanUpElement(r),e.removeChild(r)}}function swapWithStyle(e,t,n,r,a){switch(e){case"none":return;case"outerHTML":swapOuterHTML(n,r,a);return;case"afterbegin":swapAfterBegin(n,r,a);return;case"beforebegin":swapBeforeBegin(n,r,a);return;case"beforeend":swapBeforeEnd(n,r,a);return;case"afterend":swapAfterEnd(n,r,a);return;case"delete":swapDelete(n);return;default:var o=getExtensions(t);for(let u=0;u<o.length;u++){const E=o[u];try{const b=E.handleSwap(e,n,r,a);if(b){if(Array.isArray(b))for(let _=0;_<b.length;_++){const P=b[_];P.nodeType!==Node.TEXT_NODE&&P.nodeType!==Node.COMMENT_NODE&&a.tasks.push(makeAjaxLoadTask(P))}return}}catch(b){logError(b)}}e==="innerHTML"?swapInnerHTML(n,r,a):swapWithStyle(htmx.config.defaultSwapStyle,t,n,r,a)}}function findAndSwapOobElements(e,t,n){var r=findAll(e,"[hx-swap-oob], [data-hx-swap-oob]");return forEach(r,function(a){if(htmx.config.allowNestedOobSwaps||a.parentElement===null){const o=getAttributeValue(a,"hx-swap-oob");o!=null&&oobSwap(o,a,t,n)}else a.removeAttribute("hx-swap-oob"),a.removeAttribute("data-hx-swap-oob")}),r.length>0}function swap(e,t,n,r){r||(r={}),e=resolveTarget(e);const a=r.contextElement?getRootNode(r.contextElement,!1):getDocument(),o=document.activeElement;let u={};try{u={elt:o,start:o?o.selectionStart:null,end:o?o.selectionEnd:null}}catch{}const E=makeSettleInfo(e);if(n.swapStyle==="textContent")e.textContent=t;else{let _=makeFragment(t);if(E.title=_.title,r.selectOOB){const P=r.selectOOB.split(",");for(let j=0;j<P.length;j++){const $=P[j].split(":",2);let J=$[0].trim();J.indexOf("#")===0&&(J=J.substring(1));const re=$[1]||"true",ce=_.querySelector("#"+J);ce&&oobSwap(re,ce,E,a)}}if(findAndSwapOobElements(_,E,a),forEach(findAll(_,"template"),function(P){P.content&&findAndSwapOobElements(P.content,E,a)&&P.remove()}),r.select){const P=getDocument().createDocumentFragment();forEach(_.querySelectorAll(r.select),function(j){P.appendChild(j)}),_=P}handlePreservedElements(_),swapWithStyle(n.swapStyle,r.contextElement,e,_,E),restorePreservedElements()}if(u.elt&&!bodyContains(u.elt)&&getRawAttribute(u.elt,"id")){const _=document.getElementById(getRawAttribute(u.elt,"id")),P={preventScroll:n.focusScroll!==void 0?!n.focusScroll:!htmx.config.defaultFocusScroll};if(_){if(u.start&&_.setSelectionRange)try{_.setSelectionRange(u.start,u.end)}catch{}_.focus(P)}}e.classList.remove(htmx.config.swappingClass),forEach(E.elts,function(_){_.classList&&_.classList.add(htmx.config.settlingClass),triggerEvent(_,"htmx:afterSwap",r.eventInfo)}),r.afterSwapCallback&&r.afterSwapCallback(),n.ignoreTitle||handleTitle(E.title);const b=function(){if(forEach(E.tasks,function(_){_.call()}),forEach(E.elts,function(_){_.classList&&_.classList.remove(htmx.config.settlingClass),triggerEvent(_,"htmx:afterSettle",r.eventInfo)}),r.anchor){const _=asElement(resolveTarget("#"+r.anchor));_&&_.scrollIntoView({block:"start",behavior:"auto"})}updateScrollState(E.elts,n),r.afterSettleCallback&&r.afterSettleCallback()};n.settleDelay>0?getWindow().setTimeout(b,n.settleDelay):b()}function handleTriggerHeader(e,t,n){const r=e.getResponseHeader(t);if(r.indexOf("{")===0){const a=parseJSON(r);for(const o in a)if(a.hasOwnProperty(o)){let u=a[o];isRawObject(u)?n=u.target!==void 0?u.target:n:u={value:u},triggerEvent(n,o,u)}}else{const a=r.split(",");for(let o=0;o<a.length;o++)triggerEvent(n,a[o].trim(),[])}}const WHITESPACE_OR_COMMA=/[\s,]/,SYMBOL_START=/[_$a-zA-Z]/,SYMBOL_CONT=/[_$a-zA-Z0-9]/,STRINGISH_START=['"',"'","/"],NOT_WHITESPACE=/[^\s]/,COMBINED_SELECTOR_START=/[{(]/,COMBINED_SELECTOR_END=/[})]/;function tokenizeString(e){const t=[];let n=0;for(;n<e.length;){if(SYMBOL_START.exec(e.charAt(n))){for(var r=n;SYMBOL_CONT.exec(e.charAt(n+1));)n++;t.push(e.substring(r,n+1))}else if(STRINGISH_START.indexOf(e.charAt(n))!==-1){const a=e.charAt(n);var r=n;for(n++;n<e.length&&e.charAt(n)!==a;)e.charAt(n)==="\\"&&n++,n++;t.push(e.substring(r,n+1))}else{const a=e.charAt(n);t.push(a)}n++}return t}function isPossibleRelativeReference(e,t,n){return SYMBOL_START.exec(e.charAt(0))&&e!=="true"&&e!=="false"&&e!=="this"&&e!==n&&t!=="."}function maybeGenerateConditional(e,t,n){if(t[0]==="["){t.shift();let r=1,a=" return (function("+n+"){ return (",o=null;for(;t.length>0;){const u=t[0];if(u==="]"){if(r--,r===0){o===null&&(a=a+"true"),t.shift(),a+=")})";try{const E=maybeEval(e,function(){return Function(a)()},function(){return!0});return E.source=a,E}catch(E){return triggerErrorEvent(getDocument().body,"htmx:syntax:error",{error:E,source:a}),null}}}else u==="["&&r++;isPossibleRelativeReference(u,o,n)?a+="(("+n+"."+u+") ? ("+n+"."+u+") : (window."+u+"))":a=a+u,o=t.shift()}}}function consumeUntil(e,t){let n="";for(;e.length>0&&!t.test(e[0]);)n+=e.shift();return n}function consumeCSSSelector(e){let t;return e.length>0&&COMBINED_SELECTOR_START.test(e[0])?(e.shift(),t=consumeUntil(e,COMBINED_SELECTOR_END).trim(),e.shift()):t=consumeUntil(e,WHITESPACE_OR_COMMA),t}const INPUT_SELECTOR="input, textarea, select";function parseAndCacheTrigger(e,t,n){const r=[],a=tokenizeString(t);do{consumeUntil(a,NOT_WHITESPACE);const E=a.length,b=consumeUntil(a,/[,\[\s]/);if(b!=="")if(b==="every"){const _={trigger:"every"};consumeUntil(a,NOT_WHITESPACE),_.pollInterval=parseInterval(consumeUntil(a,/[,\[\s]/)),consumeUntil(a,NOT_WHITESPACE);var o=maybeGenerateConditional(e,a,"event");o&&(_.eventFilter=o),r.push(_)}else{const _={trigger:b};var o=maybeGenerateConditional(e,a,"event");for(o&&(_.eventFilter=o),consumeUntil(a,NOT_WHITESPACE);a.length>0&&a[0]!==",";){const j=a.shift();if(j==="changed")_.changed=!0;else if(j==="once")_.once=!0;else if(j==="consume")_.consume=!0;else if(j==="delay"&&a[0]===":")a.shift(),_.delay=parseInterval(consumeUntil(a,WHITESPACE_OR_COMMA));else if(j==="from"&&a[0]===":"){if(a.shift(),COMBINED_SELECTOR_START.test(a[0]))var u=consumeCSSSelector(a);else{var u=consumeUntil(a,WHITESPACE_OR_COMMA);if(u==="closest"||u==="find"||u==="next"||u==="previous"){a.shift();const J=consumeCSSSelector(a);J.length>0&&(u+=" "+J)}}_.from=u}else j==="target"&&a[0]===":"?(a.shift(),_.target=consumeCSSSelector(a)):j==="throttle"&&a[0]===":"?(a.shift(),_.throttle=parseInterval(consumeUntil(a,WHITESPACE_OR_COMMA))):j==="queue"&&a[0]===":"?(a.shift(),_.queue=consumeUntil(a,WHITESPACE_OR_COMMA)):j==="root"&&a[0]===":"?(a.shift(),_[j]=consumeCSSSelector(a)):j==="threshold"&&a[0]===":"?(a.shift(),_[j]=consumeUntil(a,WHITESPACE_OR_COMMA)):triggerErrorEvent(e,"htmx:syntax:error",{token:a.shift()});consumeUntil(a,NOT_WHITESPACE)}r.push(_)}a.length===E&&triggerErrorEvent(e,"htmx:syntax:error",{token:a.shift()}),consumeUntil(a,NOT_WHITESPACE)}while(a[0]===","&&a.shift());return n&&(n[t]=r),r}function getTriggerSpecs(e){const t=getAttributeValue(e,"hx-trigger");let n=[];if(t){const r=htmx.config.triggerSpecsCache;n=r&&r[t]||parseAndCacheTrigger(e,t,r)}return n.length>0?n:matches(e,"form")?[{trigger:"submit"}]:matches(e,'input[type="button"], input[type="submit"]')?[{trigger:"click"}]:matches(e,INPUT_SELECTOR)?[{trigger:"change"}]:[{trigger:"click"}]}function cancelPolling(e){getInternalData(e).cancelled=!0}function processPolling(e,t,n){const r=getInternalData(e);r.timeout=getWindow().setTimeout(function(){bodyContains(e)&&r.cancelled!==!0&&(maybeFilterEvent(n,e,makeEvent("hx:poll:trigger",{triggerSpec:n,target:e}))||t(e),processPolling(e,t,n))},n.pollInterval)}function isLocalLink(e){return location.hostname===e.hostname&&getRawAttribute(e,"href")&&getRawAttribute(e,"href").indexOf("#")!==0}function eltIsDisabled(e){return closest(e,htmx.config.disableSelector)}function boostElement(e,t,n){if(e instanceof HTMLAnchorElement&&isLocalLink(e)&&(e.target===""||e.target==="_self")||e.tagName==="FORM"&&String(getRawAttribute(e,"method")).toLowerCase()!=="dialog"){t.boosted=!0;let r,a;if(e.tagName==="A")r="get",a=getRawAttribute(e,"href");else{const o=getRawAttribute(e,"method");r=o?o.toLowerCase():"get",a=getRawAttribute(e,"action"),(a==null||a==="")&&(a=getDocument().location.href),r==="get"&&a.includes("?")&&(a=a.replace(/\?[^#]+/,""))}n.forEach(function(o){addEventListener(e,function(u,E){const b=asElement(u);if(eltIsDisabled(b)){cleanUpElement(b);return}issueAjaxRequest(r,a,b,E)},t,o,!0)})}}function shouldCancel(e,t){const n=asElement(t);return n?!!((e.type==="submit"||e.type==="click")&&(n.tagName==="FORM"||matches(n,'input[type="submit"], button')&&(matches(n,"[form]")||closest(n,"form")!==null)||n instanceof HTMLAnchorElement&&n.href&&(n.getAttribute("href")==="#"||n.getAttribute("href").indexOf("#")!==0))):!1}function ignoreBoostedAnchorCtrlClick(e,t){return getInternalData(e).boosted&&e instanceof HTMLAnchorElement&&t.type==="click"&&(t.ctrlKey||t.metaKey)}function maybeFilterEvent(e,t,n){const r=e.eventFilter;if(r)try{return r.call(t,n)!==!0}catch(a){const o=r.source;return triggerErrorEvent(getDocument().body,"htmx:eventFilter:error",{error:a,source:o}),!0}return!1}function addEventListener(e,t,n,r,a){const o=getInternalData(e);let u;r.from?u=querySelectorAllExt(e,r.from):u=[e],r.changed&&("lastValue"in o||(o.lastValue=new WeakMap),u.forEach(function(E){o.lastValue.has(r)||o.lastValue.set(r,new WeakMap),o.lastValue.get(r).set(E,E.value)})),forEach(u,function(E){const b=function(_){if(!bodyContains(e)){E.removeEventListener(r.trigger,b);return}if(ignoreBoostedAnchorCtrlClick(e,_)||((a||shouldCancel(_,e))&&_.preventDefault(),maybeFilterEvent(r,e,_)))return;const P=getInternalData(_);if(P.triggerSpec=r,P.handledFor==null&&(P.handledFor=[]),P.handledFor.indexOf(e)<0){if(P.handledFor.push(e),r.consume&&_.stopPropagation(),r.target&&_.target&&!matches(asElement(_.target),r.target))return;if(r.once){if(o.triggeredOnce)return;o.triggeredOnce=!0}if(r.changed){const j=event.target,$=j.value,J=o.lastValue.get(r);if(J.has(j)&&J.get(j)===$)return;J.set(j,$)}if(o.delayed&&clearTimeout(o.delayed),o.throttle)return;r.throttle>0?o.throttle||(triggerEvent(e,"htmx:trigger"),t(e,_),o.throttle=getWindow().setTimeout(function(){o.throttle=null},r.throttle)):r.delay>0?o.delayed=getWindow().setTimeout(function(){triggerEvent(e,"htmx:trigger"),t(e,_)},r.delay):(triggerEvent(e,"htmx:trigger"),t(e,_))}};n.listenerInfos==null&&(n.listenerInfos=[]),n.listenerInfos.push({trigger:r.trigger,listener:b,on:E}),E.addEventListener(r.trigger,b)})}let windowIsScrolling=!1,scrollHandler=null;function initScrollHandler(){scrollHandler||(scrollHandler=function(){windowIsScrolling=!0},window.addEventListener("scroll",scrollHandler),window.addEventListener("resize",scrollHandler),setInterval(function(){windowIsScrolling&&(windowIsScrolling=!1,forEach(getDocument().querySelectorAll("[hx-trigger*='revealed'],[data-hx-trigger*='revealed']"),function(e){maybeReveal(e)}))},200))}function maybeReveal(e){!hasAttribute(e,"data-hx-revealed")&&isScrolledIntoView(e)&&(e.setAttribute("data-hx-revealed","true"),getInternalData(e).initHash?triggerEvent(e,"revealed"):e.addEventListener("htmx:afterProcessNode",function(){triggerEvent(e,"revealed")},{once:!0}))}function loadImmediately(e,t,n,r){const a=function(){n.loaded||(n.loaded=!0,triggerEvent(e,"htmx:trigger"),t(e))};r>0?getWindow().setTimeout(a,r):a()}function processVerbs(e,t,n){let r=!1;return forEach(VERBS,function(a){if(hasAttribute(e,"hx-"+a)){const o=getAttributeValue(e,"hx-"+a);r=!0,t.path=o,t.verb=a,n.forEach(function(u){addTriggerHandler(e,u,t,function(E,b){const _=asElement(E);if(closest(_,htmx.config.disableSelector)){cleanUpElement(_);return}issueAjaxRequest(a,o,_,b)})})}}),r}function addTriggerHandler(e,t,n,r){if(t.trigger==="revealed")initScrollHandler(),addEventListener(e,r,n,t),maybeReveal(asElement(e));else if(t.trigger==="intersect"){const a={};t.root&&(a.root=querySelectorExt(e,t.root)),t.threshold&&(a.threshold=parseFloat(t.threshold)),new IntersectionObserver(function(u){for(let E=0;E<u.length;E++)if(u[E].isIntersecting){triggerEvent(e,"intersect");break}},a).observe(asElement(e)),addEventListener(asElement(e),r,n,t)}else!n.firstInitCompleted&&t.trigger==="load"?maybeFilterEvent(t,e,makeEvent("load",{elt:e}))||loadImmediately(asElement(e),r,n,t.delay):t.pollInterval>0?(n.polling=!0,processPolling(asElement(e),r,t)):addEventListener(e,r,n,t)}function shouldProcessHxOn(e){const t=asElement(e);if(!t)return!1;const n=t.attributes;for(let r=0;r<n.length;r++){const a=n[r].name;if(startsWith(a,"hx-on:")||startsWith(a,"data-hx-on:")||startsWith(a,"hx-on-")||startsWith(a,"data-hx-on-"))return!0}return!1}const HX_ON_QUERY=new XPathEvaluator().createExpression('.//*[@*[ starts-with(name(), "hx-on:") or starts-with(name(), "data-hx-on:") or starts-with(name(), "hx-on-") or starts-with(name(), "data-hx-on-") ]]');function processHXOnRoot(e,t){shouldProcessHxOn(e)&&t.push(asElement(e));const n=HX_ON_QUERY.evaluate(e);let r=null;for(;r=n.iterateNext();)t.push(asElement(r))}function findHxOnWildcardElements(e){const t=[];if(e instanceof DocumentFragment)for(const n of e.childNodes)processHXOnRoot(n,t);else processHXOnRoot(e,t);return t}function findElementsToProcess(e){if(e.querySelectorAll){const n=", [hx-boost] a, [data-hx-boost] a, a[hx-boost], a[data-hx-boost]",r=[];for(const o in extensions){const u=extensions[o];if(u.getSelectors){var t=u.getSelectors();t&&r.push(t)}}return e.querySelectorAll(VERB_SELECTOR+n+", form, [type='submit'], [hx-ext], [data-hx-ext], [hx-trigger], [data-hx-trigger]"+r.flat().map(o=>", "+o).join(""))}else return[]}function maybeSetLastButtonClicked(e){const t=closest(asElement(e.target),"button, input[type='submit']"),n=getRelatedFormData(e);n&&(n.lastButtonClicked=t)}function maybeUnsetLastButtonClicked(e){const t=getRelatedFormData(e);t&&(t.lastButtonClicked=null)}function getRelatedFormData(e){const t=closest(asElement(e.target),"button, input[type='submit']");if(!t)return;const n=resolveTarget("#"+getRawAttribute(t,"form"),t.getRootNode())||closest(t,"form");if(n)return getInternalData(n)}function initButtonTracking(e){e.addEventListener("click",maybeSetLastButtonClicked),e.addEventListener("focusin",maybeSetLastButtonClicked),e.addEventListener("focusout",maybeUnsetLastButtonClicked)}function addHxOnEventHandler(e,t,n){const r=getInternalData(e);Array.isArray(r.onHandlers)||(r.onHandlers=[]);let a;const o=function(u){maybeEval(e,function(){eltIsDisabled(e)||(a||(a=new Function("event",n)),a.call(e,u))})};e.addEventListener(t,o),r.onHandlers.push({event:t,listener:o})}function processHxOnWildcard(e){deInitOnHandlers(e);for(let t=0;t<e.attributes.length;t++){const n=e.attributes[t].name,r=e.attributes[t].value;if(startsWith(n,"hx-on")||startsWith(n,"data-hx-on")){const a=n.indexOf("-on")+3,o=n.slice(a,a+1);if(o==="-"||o===":"){let u=n.slice(a+1);startsWith(u,":")?u="htmx"+u:startsWith(u,"-")?u="htmx:"+u.slice(1):startsWith(u,"htmx-")&&(u="htmx:"+u.slice(5)),addHxOnEventHandler(e,u,r)}}}}function initNode(e){if(closest(e,htmx.config.disableSelector)){cleanUpElement(e);return}const t=getInternalData(e),n=attributeHash(e);if(t.initHash!==n){deInitNode(e),t.initHash=n,triggerEvent(e,"htmx:beforeProcessNode");const r=getTriggerSpecs(e);processVerbs(e,t,r)||(getClosestAttributeValue(e,"hx-boost")==="true"?boostElement(e,t,r):hasAttribute(e,"hx-trigger")&&r.forEach(function(o){addTriggerHandler(e,o,t,function(){})})),(e.tagName==="FORM"||getRawAttribute(e,"type")==="submit"&&hasAttribute(e,"form"))&&initButtonTracking(e),t.firstInitCompleted=!0,triggerEvent(e,"htmx:afterProcessNode")}}function processNode(e){if(e=resolveTarget(e),closest(e,htmx.config.disableSelector)){cleanUpElement(e);return}initNode(e),forEach(findElementsToProcess(e),function(t){initNode(t)}),forEach(findHxOnWildcardElements(e),processHxOnWildcard)}function kebabEventName(e){return e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase()}function makeEvent(e,t){let n;return window.CustomEvent&&typeof window.CustomEvent=="function"?n=new CustomEvent(e,{bubbles:!0,cancelable:!0,composed:!0,detail:t}):(n=getDocument().createEvent("CustomEvent"),n.initCustomEvent(e,!0,!0,t)),n}function triggerErrorEvent(e,t,n){triggerEvent(e,t,mergeObjects({error:t},n))}function ignoreEventForLogging(e){return e==="htmx:afterProcessNode"}function withExtensions(e,t){forEach(getExtensions(e),function(n){try{t(n)}catch(r){logError(r)}})}function logError(e){console.error?console.error(e):console.log&&console.log("ERROR: ",e)}function triggerEvent(e,t,n){e=resolveTarget(e),n==null&&(n={}),n.elt=e;const r=makeEvent(t,n);htmx.logger&&!ignoreEventForLogging(t)&&htmx.logger(e,t,n),n.error&&(logError(n.error),triggerEvent(e,"htmx:error",{errorInfo:n}));let a=e.dispatchEvent(r);const o=kebabEventName(t);if(a&&o!==t){const u=makeEvent(o,r.detail);a=a&&e.dispatchEvent(u)}return withExtensions(asElement(e),function(u){a=a&&u.onEvent(t,r)!==!1&&!r.defaultPrevented}),a}let currentPathForHistory=location.pathname+location.search;function getHistoryElement(){return getDocument().querySelector("[hx-history-elt],[data-hx-history-elt]")||getDocument().body}function saveToHistoryCache(e,t){if(!canAccessLocalStorage())return;const n=cleanInnerHtmlForHistory(t),r=getDocument().title,a=window.scrollY;if(htmx.config.historyCacheSize<=0){localStorage.removeItem("htmx-history-cache");return}e=normalizePath(e);const o=parseJSON(localStorage.getItem("htmx-history-cache"))||[];for(let E=0;E<o.length;E++)if(o[E].url===e){o.splice(E,1);break}const u={url:e,content:n,title:r,scroll:a};for(triggerEvent(getDocument().body,"htmx:historyItemCreated",{item:u,cache:o}),o.push(u);o.length>htmx.config.historyCacheSize;)o.shift();for(;o.length>0;)try{localStorage.setItem("htmx-history-cache",JSON.stringify(o));break}catch(E){triggerErrorEvent(getDocument().body,"htmx:historyCacheError",{cause:E,cache:o}),o.shift()}}function getCachedHistory(e){if(!canAccessLocalStorage())return null;e=normalizePath(e);const t=parseJSON(localStorage.getItem("htmx-history-cache"))||[];for(let n=0;n<t.length;n++)if(t[n].url===e)return t[n];return null}function cleanInnerHtmlForHistory(e){const t=htmx.config.requestClass,n=e.cloneNode(!0);return forEach(findAll(n,"."+t),function(r){removeClassFromElement(r,t)}),forEach(findAll(n,"[data-disabled-by-htmx]"),function(r){r.removeAttribute("disabled")}),n.innerHTML}function saveCurrentPageToHistory(){const e=getHistoryElement(),t=currentPathForHistory||location.pathname+location.search;let n;try{n=getDocument().querySelector('[hx-history="false" i],[data-hx-history="false" i]')}catch{n=getDocument().querySelector('[hx-history="false"],[data-hx-history="false"]')}n||(triggerEvent(getDocument().body,"htmx:beforeHistorySave",{path:t,historyElt:e}),saveToHistoryCache(t,e)),htmx.config.historyEnabled&&history.replaceState({htmx:!0},getDocument().title,window.location.href)}function pushUrlIntoHistory(e){htmx.config.getCacheBusterParam&&(e=e.replace(/org\.htmx\.cache-buster=[^&]*&?/,""),(endsWith(e,"&")||endsWith(e,"?"))&&(e=e.slice(0,-1))),htmx.config.historyEnabled&&history.pushState({htmx:!0},"",e),currentPathForHistory=e}function replaceUrlInHistory(e){htmx.config.historyEnabled&&history.replaceState({htmx:!0},"",e),currentPathForHistory=e}function settleImmediately(e){forEach(e,function(t){t.call(void 0)})}function loadHistoryFromServer(e){const t=new XMLHttpRequest,n={path:e,xhr:t};triggerEvent(getDocument().body,"htmx:historyCacheMiss",n),t.open("GET",e,!0),t.setRequestHeader("HX-Request","true"),t.setRequestHeader("HX-History-Restore-Request","true"),t.setRequestHeader("HX-Current-URL",getDocument().location.href),t.onload=function(){if(this.status>=200&&this.status<400){triggerEvent(getDocument().body,"htmx:historyCacheMissLoad",n);const r=makeFragment(this.response),a=r.querySelector("[hx-history-elt],[data-hx-history-elt]")||r,o=getHistoryElement(),u=makeSettleInfo(o);handleTitle(r.title),handlePreservedElements(r),swapInnerHTML(o,a,u),restorePreservedElements(),settleImmediately(u.tasks),currentPathForHistory=e,triggerEvent(getDocument().body,"htmx:historyRestore",{path:e,cacheMiss:!0,serverResponse:this.response})}else triggerErrorEvent(getDocument().body,"htmx:historyCacheMissLoadError",n)},t.send()}function restoreHistory(e){saveCurrentPageToHistory(),e=e||location.pathname+location.search;const t=getCachedHistory(e);if(t){const n=makeFragment(t.content),r=getHistoryElement(),a=makeSettleInfo(r);handleTitle(t.title),handlePreservedElements(n),swapInnerHTML(r,n,a),restorePreservedElements(),settleImmediately(a.tasks),getWindow().setTimeout(function(){window.scrollTo(0,t.scroll)},0),currentPathForHistory=e,triggerEvent(getDocument().body,"htmx:historyRestore",{path:e,item:t})}else htmx.config.refreshOnHistoryMiss?window.location.reload(!0):loadHistoryFromServer(e)}function addRequestIndicatorClasses(e){let t=findAttributeTargets(e,"hx-indicator");return t==null&&(t=[e]),forEach(t,function(n){const r=getInternalData(n);r.requestCount=(r.requestCount||0)+1,n.classList.add.call(n.classList,htmx.config.requestClass)}),t}function disableElements(e){let t=findAttributeTargets(e,"hx-disabled-elt");return t==null&&(t=[]),forEach(t,function(n){const r=getInternalData(n);r.requestCount=(r.requestCount||0)+1,n.setAttribute("disabled",""),n.setAttribute("data-disabled-by-htmx","")}),t}function removeRequestIndicators(e,t){forEach(e.concat(t),function(n){const r=getInternalData(n);r.requestCount=(r.requestCount||1)-1}),forEach(e,function(n){getInternalData(n).requestCount===0&&n.classList.remove.call(n.classList,htmx.config.requestClass)}),forEach(t,function(n){getInternalData(n).requestCount===0&&(n.removeAttribute("disabled"),n.removeAttribute("data-disabled-by-htmx"))})}function haveSeenNode(e,t){for(let n=0;n<e.length;n++)if(e[n].isSameNode(t))return!0;return!1}function shouldInclude(e){const t=e;return t.name===""||t.name==null||t.disabled||closest(t,"fieldset[disabled]")||t.type==="button"||t.type==="submit"||t.tagName==="image"||t.tagName==="reset"||t.tagName==="file"?!1:t.type==="checkbox"||t.type==="radio"?t.checked:!0}function addValueToFormData(e,t,n){e!=null&&t!=null&&(Array.isArray(t)?t.forEach(function(r){n.append(e,r)}):n.append(e,t))}function removeValueFromFormData(e,t,n){if(e!=null&&t!=null){let r=n.getAll(e);Array.isArray(t)?r=r.filter(a=>t.indexOf(a)<0):r=r.filter(a=>a!==t),n.delete(e),forEach(r,a=>n.append(e,a))}}function processInputValue(e,t,n,r,a){if(!(r==null||haveSeenNode(e,r))){if(e.push(r),shouldInclude(r)){const o=getRawAttribute(r,"name");let u=r.value;r instanceof HTMLSelectElement&&r.multiple&&(u=toArray(r.querySelectorAll("option:checked")).map(function(E){return E.value})),r instanceof HTMLInputElement&&r.files&&(u=toArray(r.files)),addValueToFormData(o,u,t),a&&validateElement(r,n)}r instanceof HTMLFormElement&&(forEach(r.elements,function(o){e.indexOf(o)>=0?removeValueFromFormData(o.name,o.value,t):e.push(o),a&&validateElement(o,n)}),new FormData(r).forEach(function(o,u){o instanceof File&&o.name===""||addValueToFormData(u,o,t)}))}}function validateElement(e,t){const n=e;n.willValidate&&(triggerEvent(n,"htmx:validation:validate"),n.checkValidity()||(t.push({elt:n,message:n.validationMessage,validity:n.validity}),triggerEvent(n,"htmx:validation:failed",{message:n.validationMessage,validity:n.validity})))}function overrideFormData(e,t){for(const n of t.keys())e.delete(n);return t.forEach(function(n,r){e.append(r,n)}),e}function getInputValues(e,t){const n=[],r=new FormData,a=new FormData,o=[],u=getInternalData(e);u.lastButtonClicked&&!bodyContains(u.lastButtonClicked)&&(u.lastButtonClicked=null);let E=e instanceof HTMLFormElement&&e.noValidate!==!0||getAttributeValue(e,"hx-validate")==="true";if(u.lastButtonClicked&&(E=E&&u.lastButtonClicked.formNoValidate!==!0),t!=="get"&&processInputValue(n,a,o,closest(e,"form"),E),processInputValue(n,r,o,e,E),u.lastButtonClicked||e.tagName==="BUTTON"||e.tagName==="INPUT"&&getRawAttribute(e,"type")==="submit"){const _=u.lastButtonClicked||e,P=getRawAttribute(_,"name");addValueToFormData(P,_.value,a)}const b=findAttributeTargets(e,"hx-include");return forEach(b,function(_){processInputValue(n,r,o,asElement(_),E),matches(_,"form")||forEach(asParentNode(_).querySelectorAll(INPUT_SELECTOR),function(P){processInputValue(n,r,o,P,E)})}),overrideFormData(r,a),{errors:o,formData:r,values:formDataProxy(r)}}function appendParam(e,t,n){e!==""&&(e+="&"),String(n)==="[object Object]"&&(n=JSON.stringify(n));const r=encodeURIComponent(n);return e+=encodeURIComponent(t)+"="+r,e}function urlEncode(e){e=formDataFromObject(e);let t="";return e.forEach(function(n,r){t=appendParam(t,r,n)}),t}function getHeaders(e,t,n){const r={"HX-Request":"true","HX-Trigger":getRawAttribute(e,"id"),"HX-Trigger-Name":getRawAttribute(e,"name"),"HX-Target":getAttributeValue(t,"id"),"HX-Current-URL":getDocument().location.href};return getValuesForElement(e,"hx-headers",!1,r),n!==void 0&&(r["HX-Prompt"]=n),getInternalData(e).boosted&&(r["HX-Boosted"]="true"),r}function filterValues(e,t){const n=getClosestAttributeValue(t,"hx-params");if(n){if(n==="none")return new FormData;if(n==="*")return e;if(n.indexOf("not ")===0)return forEach(n.slice(4).split(","),function(r){r=r.trim(),e.delete(r)}),e;{const r=new FormData;return forEach(n.split(","),function(a){a=a.trim(),e.has(a)&&e.getAll(a).forEach(function(o){r.append(a,o)})}),r}}else return e}function isAnchorLink(e){return!!getRawAttribute(e,"href")&&getRawAttribute(e,"href").indexOf("#")>=0}function getSwapSpecification(e,t){const n=t||getClosestAttributeValue(e,"hx-swap"),r={swapStyle:getInternalData(e).boosted?"innerHTML":htmx.config.defaultSwapStyle,swapDelay:htmx.config.defaultSwapDelay,settleDelay:htmx.config.defaultSettleDelay};if(htmx.config.scrollIntoViewOnBoost&&getInternalData(e).boosted&&!isAnchorLink(e)&&(r.show="top"),n){const u=splitOnWhitespace(n);if(u.length>0)for(let E=0;E<u.length;E++){const b=u[E];if(b.indexOf("swap:")===0)r.swapDelay=parseInterval(b.slice(5));else if(b.indexOf("settle:")===0)r.settleDelay=parseInterval(b.slice(7));else if(b.indexOf("transition:")===0)r.transition=b.slice(11)==="true";else if(b.indexOf("ignoreTitle:")===0)r.ignoreTitle=b.slice(12)==="true";else if(b.indexOf("scroll:")===0){var a=b.slice(7).split(":");const P=a.pop();var o=a.length>0?a.join(":"):null;r.scroll=P,r.scrollTarget=o}else if(b.indexOf("show:")===0){var a=b.slice(5).split(":");const j=a.pop();var o=a.length>0?a.join(":"):null;r.show=j,r.showTarget=o}else if(b.indexOf("focus-scroll:")===0){const _=b.slice(13);r.focusScroll=_=="true"}else E==0?r.swapStyle=b:logError("Unknown modifier in hx-swap: "+b)}}return r}function usesFormData(e){return getClosestAttributeValue(e,"hx-encoding")==="multipart/form-data"||matches(e,"form")&&getRawAttribute(e,"enctype")==="multipart/form-data"}function encodeParamsForBody(e,t,n){let r=null;return withExtensions(t,function(a){r==null&&(r=a.encodeParameters(e,n,t))}),r??(usesFormData(t)?overrideFormData(new FormData,formDataFromObject(n)):urlEncode(n))}function makeSettleInfo(e){return{tasks:[],elts:[e]}}function updateScrollState(e,t){const n=e[0],r=e[e.length-1];if(t.scroll){var a=null;t.scrollTarget&&(a=asElement(querySelectorExt(n,t.scrollTarget))),t.scroll==="top"&&(n||a)&&(a=a||n,a.scrollTop=0),t.scroll==="bottom"&&(r||a)&&(a=a||r,a.scrollTop=a.scrollHeight)}if(t.show){var a=null;if(t.showTarget){let u=t.showTarget;t.showTarget==="window"&&(u="body"),a=asElement(querySelectorExt(n,u))}t.show==="top"&&(n||a)&&(a=a||n,a.scrollIntoView({block:"start",behavior:htmx.config.scrollBehavior})),t.show==="bottom"&&(r||a)&&(a=a||r,a.scrollIntoView({block:"end",behavior:htmx.config.scrollBehavior}))}}function getValuesForElement(e,t,n,r){if(r==null&&(r={}),e==null)return r;const a=getAttributeValue(e,t);if(a){let o=a.trim(),u=n;if(o==="unset")return null;o.indexOf("javascript:")===0?(o=o.slice(11),u=!0):o.indexOf("js:")===0&&(o=o.slice(3),u=!0),o.indexOf("{")!==0&&(o="{"+o+"}");let E;u?E=maybeEval(e,function(){return Function("return ("+o+")")()},{}):E=parseJSON(o);for(const b in E)E.hasOwnProperty(b)&&r[b]==null&&(r[b]=E[b])}return getValuesForElement(asElement(parentElt(e)),t,n,r)}function maybeEval(e,t,n){return htmx.config.allowEval?t():(triggerErrorEvent(e,"htmx:evalDisallowedError"),n)}function getHXVarsForElement(e,t){return getValuesForElement(e,"hx-vars",!0,t)}function getHXValsForElement(e,t){return getValuesForElement(e,"hx-vals",!1,t)}function getExpressionVars(e){return mergeObjects(getHXVarsForElement(e),getHXValsForElement(e))}function safelySetHeaderValue(e,t,n){if(n!==null)try{e.setRequestHeader(t,n)}catch{e.setRequestHeader(t,encodeURIComponent(n)),e.setRequestHeader(t+"-URI-AutoEncoded","true")}}function getPathFromResponse(e){if(e.responseURL&&typeof URL<"u")try{const t=new URL(e.responseURL);return t.pathname+t.search}catch{triggerErrorEvent(getDocument().body,"htmx:badResponseUrl",{url:e.responseURL})}}function hasHeader(e,t){return t.test(e.getAllResponseHeaders())}function ajaxHelper(e,t,n){if(e=e.toLowerCase(),n){if(n instanceof Element||typeof n=="string")return issueAjaxRequest(e,t,null,null,{targetOverride:resolveTarget(n)||DUMMY_ELT,returnPromise:!0});{let r=resolveTarget(n.target);return(n.target&&!r||n.source&&!r&&!resolveTarget(n.source))&&(r=DUMMY_ELT),issueAjaxRequest(e,t,resolveTarget(n.source),n.event,{handler:n.handler,headers:n.headers,values:n.values,targetOverride:r,swapOverride:n.swap,select:n.select,returnPromise:!0})}}else return issueAjaxRequest(e,t,null,null,{returnPromise:!0})}function hierarchyForElt(e){const t=[];for(;e;)t.push(e),e=e.parentElement;return t}function verifyPath(e,t,n){let r,a;return typeof URL=="function"?(a=new URL(t,document.location.href),r=document.location.origin===a.origin):(a=t,r=startsWith(t,document.location.origin)),htmx.config.selfRequestsOnly&&!r?!1:triggerEvent(e,"htmx:validateUrl",mergeObjects({url:a,sameHost:r},n))}function formDataFromObject(e){if(e instanceof FormData)return e;const t=new FormData;for(const n in e)e.hasOwnProperty(n)&&(e[n]&&typeof e[n].forEach=="function"?e[n].forEach(function(r){t.append(n,r)}):typeof e[n]=="object"&&!(e[n]instanceof Blob)?t.append(n,JSON.stringify(e[n])):t.append(n,e[n]));return t}function formDataArrayProxy(e,t,n){return new Proxy(n,{get:function(r,a){return typeof a=="number"?r[a]:a==="length"?r.length:a==="push"?function(o){r.push(o),e.append(t,o)}:typeof r[a]=="function"?function(){r[a].apply(r,arguments),e.delete(t),r.forEach(function(o){e.append(t,o)})}:r[a]&&r[a].length===1?r[a][0]:r[a]},set:function(r,a,o){return r[a]=o,e.delete(t),r.forEach(function(u){e.append(t,u)}),!0}})}function formDataProxy(e){return new Proxy(e,{get:function(t,n){if(typeof n=="symbol"){const a=Reflect.get(t,n);return typeof a=="function"?function(){return a.apply(e,arguments)}:a}if(n==="toJSON")return()=>Object.fromEntries(e);if(n in t)return typeof t[n]=="function"?function(){return e[n].apply(e,arguments)}:t[n];const r=e.getAll(n);if(r.length!==0)return r.length===1?r[0]:formDataArrayProxy(t,n,r)},set:function(t,n,r){return typeof n!="string"?!1:(t.delete(n),r&&typeof r.forEach=="function"?r.forEach(function(a){t.append(n,a)}):typeof r=="object"&&!(r instanceof Blob)?t.append(n,JSON.stringify(r)):t.append(n,r),!0)},deleteProperty:function(t,n){return typeof n=="string"&&t.delete(n),!0},ownKeys:function(t){return Reflect.ownKeys(Object.fromEntries(t))},getOwnPropertyDescriptor:function(t,n){return Reflect.getOwnPropertyDescriptor(Object.fromEntries(t),n)}})}function issueAjaxRequest(e,t,n,r,a,o){let u=null,E=null;if(a=a??{},a.returnPromise&&typeof Promise<"u")var b=new Promise(function(I,L){u=I,E=L});n==null&&(n=getDocument().body);const _=a.handler||handleAjaxResponse,P=a.select||null;if(!bodyContains(n))return maybeCall(u),b;const j=a.targetOverride||asElement(getTarget(n));if(j==null||j==DUMMY_ELT)return triggerErrorEvent(n,"htmx:targetError",{target:getAttributeValue(n,"hx-target")}),maybeCall(E),b;let $=getInternalData(n);const J=$.lastButtonClicked;if(J){const I=getRawAttribute(J,"formaction");I!=null&&(t=I);const L=getRawAttribute(J,"formmethod");L!=null&&L.toLowerCase()!=="dialog"&&(e=L)}const re=getClosestAttributeValue(n,"hx-confirm");if(o===void 0&&triggerEvent(n,"htmx:confirm",{target:j,elt:n,path:t,verb:e,triggeringEvent:r,etc:a,issueRequest:function(F){return issueAjaxRequest(e,t,n,r,a,!!F)},question:re})===!1)return maybeCall(u),b;let ce=n,W=getClosestAttributeValue(n,"hx-sync"),Q=null,te=!1;if(W){const I=W.split(":"),L=I[0].trim();if(L==="this"?ce=findThisElement(n,"hx-sync"):ce=asElement(querySelectorExt(n,L)),W=(I[1]||"drop").trim(),$=getInternalData(ce),W==="drop"&&$.xhr&&$.abortable!==!0)return maybeCall(u),b;if(W==="abort"){if($.xhr)return maybeCall(u),b;te=!0}else W==="replace"?triggerEvent(ce,"htmx:abort"):W.indexOf("queue")===0&&(Q=(W.split(" ")[1]||"last").trim())}if($.xhr)if($.abortable)triggerEvent(ce,"htmx:abort");else{if(Q==null){if(r){const I=getInternalData(r);I&&I.triggerSpec&&I.triggerSpec.queue&&(Q=I.triggerSpec.queue)}Q==null&&(Q="last")}return $.queuedRequests==null&&($.queuedRequests=[]),Q==="first"&&$.queuedRequests.length===0?$.queuedRequests.push(function(){issueAjaxRequest(e,t,n,r,a)}):Q==="all"?$.queuedRequests.push(function(){issueAjaxRequest(e,t,n,r,a)}):Q==="last"&&($.queuedRequests=[],$.queuedRequests.push(function(){issueAjaxRequest(e,t,n,r,a)})),maybeCall(u),b}const ee=new XMLHttpRequest;$.xhr=ee,$.abortable=te;const se=function(){$.xhr=null,$.abortable=!1,$.queuedRequests!=null&&$.queuedRequests.length>0&&$.queuedRequests.shift()()},ge=getClosestAttributeValue(n,"hx-prompt");if(ge){var oe=prompt(ge);if(oe===null||!triggerEvent(n,"htmx:prompt",{prompt:oe,target:j}))return maybeCall(u),se(),b}if(re&&!o&&!confirm(re))return maybeCall(u),se(),b;let me=getHeaders(n,j,oe);e!=="get"&&!usesFormData(n)&&(me["Content-Type"]="application/x-www-form-urlencoded"),a.headers&&(me=mergeObjects(me,a.headers));const le=getInputValues(n,e);let Se=le.errors;const Ae=le.formData;a.values&&overrideFormData(Ae,formDataFromObject(a.values));const Re=formDataFromObject(getExpressionVars(n)),z=overrideFormData(Ae,Re);let he=filterValues(z,n);htmx.config.getCacheBusterParam&&e==="get"&&he.set("org.htmx.cache-buster",getRawAttribute(j,"id")||"true"),(t==null||t==="")&&(t=getDocument().location.href);const C=getValuesForElement(n,"hx-request"),s=getInternalData(n).boosted;let d=htmx.config.methodsThatUseUrlParams.indexOf(e)>=0;const y={boosted:s,useUrlParams:d,formData:he,parameters:formDataProxy(he),unfilteredFormData:z,unfilteredParameters:formDataProxy(z),headers:me,target:j,verb:e,errors:Se,withCredentials:a.credentials||C.credentials||htmx.config.withCredentials,timeout:a.timeout||C.timeout||htmx.config.timeout,path:t,triggeringEvent:r};if(!triggerEvent(n,"htmx:configRequest",y))return maybeCall(u),se(),b;if(t=y.path,e=y.verb,me=y.headers,he=formDataFromObject(y.parameters),Se=y.errors,d=y.useUrlParams,Se&&Se.length>0)return triggerEvent(n,"htmx:validation:halted",y),maybeCall(u),se(),b;const x=t.split("#"),R=x[0],k=x[1];let w=t;if(d&&(w=R,!he.keys().next().done&&(w.indexOf("?")<0?w+="?":w+="&",w+=urlEncode(he),k&&(w+="#"+k))),!verifyPath(n,w,y))return triggerErrorEvent(n,"htmx:invalidPath",y),maybeCall(E),b;if(ee.open(e.toUpperCase(),w,!0),ee.overrideMimeType("text/html"),ee.withCredentials=y.withCredentials,ee.timeout=y.timeout,!C.noHeaders){for(const I in me)if(me.hasOwnProperty(I)){const L=me[I];safelySetHeaderValue(ee,I,L)}}const p={xhr:ee,target:j,requestConfig:y,etc:a,boosted:s,select:P,pathInfo:{requestPath:t,finalRequestPath:w,responsePath:null,anchor:k}};if(ee.onload=function(){try{const I=hierarchyForElt(n);if(p.pathInfo.responsePath=getPathFromResponse(ee),_(n,p),p.keepIndicators!==!0&&removeRequestIndicators(q,D),triggerEvent(n,"htmx:afterRequest",p),triggerEvent(n,"htmx:afterOnLoad",p),!bodyContains(n)){let L=null;for(;I.length>0&&L==null;){const F=I.shift();bodyContains(F)&&(L=F)}L&&(triggerEvent(L,"htmx:afterRequest",p),triggerEvent(L,"htmx:afterOnLoad",p))}maybeCall(u),se()}catch(I){throw triggerErrorEvent(n,"htmx:onLoadError",mergeObjects({error:I},p)),I}},ee.onerror=function(){removeRequestIndicators(q,D),triggerErrorEvent(n,"htmx:afterRequest",p),triggerErrorEvent(n,"htmx:sendError",p),maybeCall(E),se()},ee.onabort=function(){removeRequestIndicators(q,D),triggerErrorEvent(n,"htmx:afterRequest",p),triggerErrorEvent(n,"htmx:sendAbort",p),maybeCall(E),se()},ee.ontimeout=function(){removeRequestIndicators(q,D),triggerErrorEvent(n,"htmx:afterRequest",p),triggerErrorEvent(n,"htmx:timeout",p),maybeCall(E),se()},!triggerEvent(n,"htmx:beforeRequest",p))return maybeCall(u),se(),b;var q=addRequestIndicatorClasses(n),D=disableElements(n);forEach(["loadstart","loadend","progress","abort"],function(I){forEach([ee,ee.upload],function(L){L.addEventListener(I,function(F){triggerEvent(n,"htmx:xhr:"+I,{lengthComputable:F.lengthComputable,loaded:F.loaded,total:F.total})})})}),triggerEvent(n,"htmx:beforeSend",p);const H=d?null:encodeParamsForBody(ee,n,he);return ee.send(H),b}function determineHistoryUpdates(e,t){const n=t.xhr;let r=null,a=null;if(hasHeader(n,/HX-Push:/i)?(r=n.getResponseHeader("HX-Push"),a="push"):hasHeader(n,/HX-Push-Url:/i)?(r=n.getResponseHeader("HX-Push-Url"),a="push"):hasHeader(n,/HX-Replace-Url:/i)&&(r=n.getResponseHeader("HX-Replace-Url"),a="replace"),r)return r==="false"?{}:{type:a,path:r};const o=t.pathInfo.finalRequestPath,u=t.pathInfo.responsePath,E=getClosestAttributeValue(e,"hx-push-url"),b=getClosestAttributeValue(e,"hx-replace-url"),_=getInternalData(e).boosted;let P=null,j=null;return E?(P="push",j=E):b?(P="replace",j=b):_&&(P="push",j=u||o),j?j==="false"?{}:(j==="true"&&(j=u||o),t.pathInfo.anchor&&j.indexOf("#")===-1&&(j=j+"#"+t.pathInfo.anchor),{type:P,path:j}):{}}function codeMatches(e,t){var n=new RegExp(e.code);return n.test(t.toString(10))}function resolveResponseHandling(e){for(var t=0;t<htmx.config.responseHandling.length;t++){var n=htmx.config.responseHandling[t];if(codeMatches(n,e.status))return n}return{swap:!1}}function handleTitle(e){if(e){const t=find("title");t?t.innerHTML=e:window.document.title=e}}function handleAjaxResponse(e,t){const n=t.xhr;let r=t.target;const a=t.etc,o=t.select;if(!triggerEvent(e,"htmx:beforeOnLoad",t))return;if(hasHeader(n,/HX-Trigger:/i)&&handleTriggerHeader(n,"HX-Trigger",e),hasHeader(n,/HX-Location:/i)){saveCurrentPageToHistory();let te=n.getResponseHeader("HX-Location");var u;te.indexOf("{")===0&&(u=parseJSON(te),te=u.path,delete u.path),ajaxHelper("get",te,u).then(function(){pushUrlIntoHistory(te)});return}const E=hasHeader(n,/HX-Refresh:/i)&&n.getResponseHeader("HX-Refresh")==="true";if(hasHeader(n,/HX-Redirect:/i)){t.keepIndicators=!0,location.href=n.getResponseHeader("HX-Redirect"),E&&location.reload();return}if(E){t.keepIndicators=!0,location.reload();return}hasHeader(n,/HX-Retarget:/i)&&(n.getResponseHeader("HX-Retarget")==="this"?t.target=e:t.target=asElement(querySelectorExt(e,n.getResponseHeader("HX-Retarget"))));const b=determineHistoryUpdates(e,t),_=resolveResponseHandling(n),P=_.swap;let j=!!_.error,$=htmx.config.ignoreTitle||_.ignoreTitle,J=_.select;_.target&&(t.target=asElement(querySelectorExt(e,_.target)));var re=a.swapOverride;re==null&&_.swapOverride&&(re=_.swapOverride),hasHeader(n,/HX-Retarget:/i)&&(n.getResponseHeader("HX-Retarget")==="this"?t.target=e:t.target=asElement(querySelectorExt(e,n.getResponseHeader("HX-Retarget")))),hasHeader(n,/HX-Reswap:/i)&&(re=n.getResponseHeader("HX-Reswap"));var ce=n.response,W=mergeObjects({shouldSwap:P,serverResponse:ce,isError:j,ignoreTitle:$,selectOverride:J,swapOverride:re},t);if(!(_.event&&!triggerEvent(r,_.event,W))&&triggerEvent(r,"htmx:beforeSwap",W)){if(r=W.target,ce=W.serverResponse,j=W.isError,$=W.ignoreTitle,J=W.selectOverride,re=W.swapOverride,t.target=r,t.failed=j,t.successful=!j,W.shouldSwap){n.status===286&&cancelPolling(e),withExtensions(e,function(le){ce=le.transformResponse(ce,n,e)}),b.type&&saveCurrentPageToHistory();var Q=getSwapSpecification(e,re);Q.hasOwnProperty("ignoreTitle")||(Q.ignoreTitle=$),r.classList.add(htmx.config.swappingClass);let te=null,ee=null;o&&(J=o),hasHeader(n,/HX-Reselect:/i)&&(J=n.getResponseHeader("HX-Reselect"));const se=getClosestAttributeValue(e,"hx-select-oob"),ge=getClosestAttributeValue(e,"hx-select");let oe=function(){try{b.type&&(triggerEvent(getDocument().body,"htmx:beforeHistoryUpdate",mergeObjects({history:b},t)),b.type==="push"?(pushUrlIntoHistory(b.path),triggerEvent(getDocument().body,"htmx:pushedIntoHistory",{path:b.path})):(replaceUrlInHistory(b.path),triggerEvent(getDocument().body,"htmx:replacedInHistory",{path:b.path}))),swap(r,ce,Q,{select:J||ge,selectOOB:se,eventInfo:t,anchor:t.pathInfo.anchor,contextElement:e,afterSwapCallback:function(){if(hasHeader(n,/HX-Trigger-After-Swap:/i)){let le=e;bodyContains(e)||(le=getDocument().body),handleTriggerHeader(n,"HX-Trigger-After-Swap",le)}},afterSettleCallback:function(){if(hasHeader(n,/HX-Trigger-After-Settle:/i)){let le=e;bodyContains(e)||(le=getDocument().body),handleTriggerHeader(n,"HX-Trigger-After-Settle",le)}maybeCall(te)}})}catch(le){throw triggerErrorEvent(e,"htmx:swapError",t),maybeCall(ee),le}},me=htmx.config.globalViewTransitions;if(Q.hasOwnProperty("transition")&&(me=Q.transition),me&&triggerEvent(e,"htmx:beforeTransition",t)&&typeof Promise<"u"&&document.startViewTransition){const le=new Promise(function(Ae,Re){te=Ae,ee=Re}),Se=oe;oe=function(){document.startViewTransition(function(){return Se(),le})}}Q.swapDelay>0?getWindow().setTimeout(oe,Q.swapDelay):oe()}j&&triggerErrorEvent(e,"htmx:responseError",mergeObjects({error:"Response Status Error Code "+n.status+" from "+t.pathInfo.requestPath},t))}}const extensions={};function extensionBase(){return{init:function(e){return null},getSelectors:function(){return null},onEvent:function(e,t){return!0},transformResponse:function(e,t,n){return e},isInlineSwap:function(e){return!1},handleSwap:function(e,t,n,r){return!1},encodeParameters:function(e,t,n){return null}}}function defineExtension(e,t){t.init&&t.init(internalAPI),extensions[e]=mergeObjects(extensionBase(),t)}function removeExtension(e){delete extensions[e]}function getExtensions(e,t,n){if(t==null&&(t=[]),e==null)return t;n==null&&(n=[]);const r=getAttributeValue(e,"hx-ext");return r&&forEach(r.split(","),function(a){if(a=a.replace(/ /g,""),a.slice(0,7)=="ignore:"){n.push(a.slice(7));return}if(n.indexOf(a)<0){const o=extensions[a];o&&t.indexOf(o)<0&&t.push(o)}}),getExtensions(asElement(parentElt(e)),t,n)}var isReady=!1;getDocument().addEventListener("DOMContentLoaded",function(){isReady=!0});function ready(e){isReady||getDocument().readyState==="complete"?e():getDocument().addEventListener("DOMContentLoaded",e)}function insertIndicatorStyles(){if(htmx.config.includeIndicatorStyles!==!1){const e=htmx.config.inlineStyleNonce?` nonce="${htmx.config.inlineStyleNonce}"`:"";getDocument().head.insertAdjacentHTML("beforeend","<style"+e+">      ."+htmx.config.indicatorClass+"{opacity:0}      ."+htmx.config.requestClass+" ."+htmx.config.indicatorClass+"{opacity:1; transition: opacity 200ms ease-in;}      ."+htmx.config.requestClass+"."+htmx.config.indicatorClass+"{opacity:1; transition: opacity 200ms ease-in;}      </style>")}}function getMetaConfig(){const e=getDocument().querySelector('meta[name="htmx-config"]');return e?parseJSON(e.content):null}function mergeMetaConfig(){const e=getMetaConfig();e&&(htmx.config=mergeObjects(htmx.config,e))}return ready(function(){mergeMetaConfig(),insertIndicatorStyles();let e=getDocument().body;processNode(e);const t=getDocument().querySelectorAll("[hx-trigger='restored'],[data-hx-trigger='restored']");e.addEventListener("htmx:abort",function(r){const a=r.target,o=getInternalData(a);o&&o.xhr&&o.xhr.abort()});const n=window.onpopstate?window.onpopstate.bind(window):null;window.onpopstate=function(r){r.state&&r.state.htmx?(restoreHistory(),forEach(t,function(a){triggerEvent(a,"htmx:restored",{document:getDocument(),triggerEvent})})):n&&n(r)},getWindow().setTimeout(function(){triggerEvent(e,"htmx:load",{}),e=null},0)}),htmx}();function getDefaultExportFromCjs(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var _hyperscript_min$1={exports:{}},_hyperscript_min=_hyperscript_min$1.exports,hasRequired_hyperscript_min;function require_hyperscript_min(){return hasRequired_hyperscript_min||(hasRequired_hyperscript_min=1,function(e,t){(function(n,r){const a=r(n);typeof t.nodeName!="string"?e.exports=a:(n._hyperscript=a,"document"in n&&n._hyperscript.browserInit())})(typeof self<"u"?self:_hyperscript_min,n=>{const r={dynamicResolvers:[function(C,s){if(C==="Fixed")return Number(s).toFixed();if(C.indexOf("Fixed:")===0){let d=C.split(":")[1];return Number(s).toFixed(parseInt(d))}}],String:function(C){return C.toString?C.toString():""+C},Int:function(C){return parseInt(C)},Float:function(C){return parseFloat(C)},Number:function(C){return Number(C)},Date:function(C){return new Date(C)},Array:function(C){return Array.from(C)},JSON:function(C){return JSON.stringify(C)},Object:function(C){return C instanceof String&&(C=C.toString()),typeof C=="string"?JSON.parse(C):Object.assign({},C)}},a={attributes:"_, script, data-script",defaultTransition:"all 500ms ease-in",disableSelector:"[disable-scripting], [data-disable-scripting]",hideShowStrategies:{},conversions:r},z=class z{static isValidCSSClassChar(s){return z.isAlpha(s)||z.isNumeric(s)||s==="-"||s==="_"||s===":"}static isValidCSSIDChar(s){return z.isAlpha(s)||z.isNumeric(s)||s==="-"||s==="_"||s===":"}static isWhitespace(s){return s===" "||s==="	"||z.isNewline(s)}static positionString(s){return"[Line: "+s.line+", Column: "+s.column+"]"}static isNewline(s){return s==="\r"||s===`
`}static isNumeric(s){return s>="0"&&s<="9"}static isAlpha(s){return s>="a"&&s<="z"||s>="A"&&s<="Z"}static isIdentifierChar(s,d){return s==="_"||s==="$"}static isReservedChar(s){return s==="`"||s==="^"}static isValidSingleQuoteStringStart(s){if(s.length>0){var d=s[s.length-1];if(d.type==="IDENTIFIER"||d.type==="CLASS_REF"||d.type==="ID_REF"||d.op&&(d.value===">"||d.value===")"))return!1}return!0}static tokenize(s,d){var y=[],x=s,R=0,k=0,w=1,p="<START>",q=0;function D(){return d&&q===0}for(;R<x.length;)if(h()==="-"&&T()==="-"&&(z.isWhitespace(S(2))||S(2)===""||S(2)==="-")||h()==="/"&&T()==="/"&&(z.isWhitespace(S(2))||S(2)===""||S(2)==="/"))L();else if(h()==="/"&&T()==="*"&&(z.isWhitespace(S(2))||S(2)===""||S(2)==="*"))F();else if(z.isWhitespace(h()))y.push(V());else if(!M()&&h()==="."&&(z.isAlpha(T())||T()==="{"||T()==="-"))y.push(B());else if(!M()&&h()==="#"&&(z.isAlpha(T())||T()==="{"))y.push(c());else if(h()==="["&&T()==="@")y.push(K());else if(h()==="@")y.push(Y());else if(h()==="*"&&z.isAlpha(T()))y.push(X());else if(D()&&(z.isAlpha(h())||h()==="\\"))y.push(f());else if(!D()&&(z.isAlpha(h())||z.isIdentifierChar(h())))y.push(i());else if(z.isNumeric(h()))y.push(l());else if(!D()&&(h()==='"'||h()==="`"))y.push(m());else if(!D()&&h()==="'")z.isValidSingleQuoteStringStart(y)?y.push(m()):y.push(v());else if(z.OP_TABLE[h()])p==="$"&&h()==="{"&&q++,h()==="}"&&q--,y.push(v());else if(D()||z.isReservedChar(h()))y.push(I("RESERVED",A()));else if(R<x.length)throw Error("Unknown token: "+h()+" ");return new u(y,[],x);function H(O,N){var G=I(O,N);return G.op=!0,G}function I(O,N){return{type:O,value:N||"",start:R,end:R+1,column:k,line:w}}function L(){for(;h()&&!z.isNewline(h());)A();A()}function F(){for(;h()&&!(h()==="*"&&T()==="/");)A();A(),A()}function B(){var O=I("CLASS_REF"),N=A();if(h()==="{"){for(O.template=!0,N+=A();h()&&h()!=="}";)N+=A();if(h()!=="}")throw Error("Unterminated class reference");N+=A()}else for(;z.isValidCSSClassChar(h())||h()==="\\";)h()==="\\"&&A(),N+=A();return O.value=N,O.end=R,O}function K(){for(var O=I("ATTRIBUTE_REF"),N=A();R<x.length&&h()!=="]";)N+=A();return h()==="]"&&(N+=A()),O.value=N,O.end=R,O}function Y(){for(var O=I("ATTRIBUTE_REF"),N=A();z.isValidCSSIDChar(h());)N+=A();if(h()==="="){if(N+=A(),h()==='"'||h()==="'"){let G=m();N+=G.value}else if(z.isAlpha(h())||z.isNumeric(h())||z.isIdentifierChar(h())){let G=i();N+=G.value}}return O.value=N,O.end=R,O}function X(){for(var O=I("STYLE_REF"),N=A();z.isAlpha(h())||h()==="-";)N+=A();return O.value=N,O.end=R,O}function c(){var O=I("ID_REF"),N=A();if(h()==="{"){for(O.template=!0,N+=A();h()&&h()!=="}";)N+=A();if(h()!=="}")throw Error("Unterminated id reference");A()}else for(;z.isValidCSSIDChar(h());)N+=A();return O.value=N,O.end=R,O}function f(){var O=I("IDENTIFIER"),N=A(),G=N==="\\";for(G&&(N="");(z.isAlpha(h())||z.isNumeric(h())||z.isIdentifierChar(h())||h()==="\\"||h()==="{"||h()==="}")&&!(h()==="$"&&G===!1);)h()==="\\"?(G=!0,A()):(G=!1,N+=A());return h()==="!"&&N==="beep"&&(N+=A()),O.value=N,O.end=R,O}function i(){for(var O=I("IDENTIFIER"),N=A();z.isAlpha(h())||z.isNumeric(h())||z.isIdentifierChar(h());)N+=A();return h()==="!"&&N==="beep"&&(N+=A()),O.value=N,O.end=R,O}function l(){for(var O=I("NUMBER"),N=A();z.isNumeric(h());)N+=A();for(h()==="."&&z.isNumeric(T())&&(N+=A());z.isNumeric(h());)N+=A();for((h()==="e"||h()==="E")&&(z.isNumeric(T())?N+=A():T()==="-"&&(N+=A(),N+=A()));z.isNumeric(h());)N+=A();return O.value=N,O.end=R,O}function v(){for(var O=H(),N=A();h()&&z.OP_TABLE[N+h()];)N+=A();return O.type=z.OP_TABLE[N],O.value=N,O.end=R,O}function m(){var O=I("STRING"),N=A();O.template=N==="`";for(var G="";h()&&h()!==N;)if(h()==="\\"){A();let U=A();if(U==="b")G+="\b";else if(U==="f")G+="\f";else if(U==="n")G+=`
`;else if(U==="r")G+="\r";else if(U==="t")G+="	";else if(U==="v")G+="\v";else if(O.template&&U==="$")G+="\\$";else if(U==="x"){const ne=g();if(Number.isNaN(ne))throw Error("Invalid hexadecimal escape at "+z.positionString(O));G+=String.fromCharCode(ne)}else G+=U}else G+=A();if(h()!==N)throw Error("Unterminated string at "+z.positionString(O));return A(),O.value=G,O.end=R,O}function g(){if(!h())return NaN;let N=16*Number.parseInt(A(),16);return h()?(N+=Number.parseInt(A(),16),N):NaN}function h(){return x.charAt(R)}function T(){return x.charAt(R+1)}function S(O=1){return x.charAt(R+O)}function A(){return p=h(),R++,k++,p}function M(){return z.isAlpha(p)||z.isNumeric(p)||p===")"||p==='"'||p==="'"||p==="`"||p==="}"||p==="]"}function V(){for(var O=I("WHITESPACE"),N="";h()&&z.isWhitespace(h());)z.isNewline(h())&&(k=0,w++),N+=A();return O.value=N,O.end=R,O}}tokenize(s,d){return z.tokenize(s,d)}};de(z,"OP_TABLE",{"+":"PLUS","-":"MINUS","*":"MULTIPLY","/":"DIVIDE",".":"PERIOD","..":"ELLIPSIS","\\":"BACKSLASH",":":"COLON","%":"PERCENT","|":"PIPE","!":"EXCLAMATION","?":"QUESTION","#":"POUND","&":"AMPERSAND",$:"DOLLAR",";":"SEMI",",":"COMMA","(":"L_PAREN",")":"R_PAREN","<":"L_ANG",">":"R_ANG","<=":"LTE_ANG",">=":"GTE_ANG","==":"EQ","===":"EQQ","!=":"NEQ","!==":"NEQQ","{":"L_BRACE","}":"R_BRACE","[":"L_BRACKET","]":"R_BRACKET","=":"EQUALS","~":"TILDE"});let o=z;class u{constructor(s,d,y){de(this,"_lastConsumed",null);de(this,"follows",[]);this.tokens=s,this.consumed=d,this.source=y,this.consumeWhitespace()}get list(){return this.tokens}consumeWhitespace(){for(;this.token(0,!0).type==="WHITESPACE";)this.consumed.push(this.tokens.shift())}raiseError(s,d){E.raiseParseError(s,d)}requireOpToken(s){var d=this.matchOpToken(s);if(d)return d;this.raiseError(this,"Expected '"+s+"' but found '"+this.currentToken().value+"'")}matchAnyOpToken(s,d,y){for(var x=0;x<arguments.length;x++){var R=arguments[x],k=this.matchOpToken(R);if(k)return k}}matchAnyToken(s,d,y){for(var x=0;x<arguments.length;x++){var R=arguments[x],k=this.matchToken(R);if(k)return k}}matchOpToken(s){if(this.currentToken()&&this.currentToken().op&&this.currentToken().value===s)return this.consumeToken()}requireTokenType(s,d,y,x){var R=this.matchTokenType(s,d,y,x);if(R)return R;this.raiseError(this,"Expected one of "+JSON.stringify([s,d,y]))}matchTokenType(s,d,y,x){if(this.currentToken()&&this.currentToken().type&&[s,d,y,x].indexOf(this.currentToken().type)>=0)return this.consumeToken()}requireToken(s,d){var y=this.matchToken(s,d);if(y)return y;this.raiseError(this,"Expected '"+s+"' but found '"+this.currentToken().value+"'")}peekToken(s,d,y){if(d=d||0,y=y||"IDENTIFIER",this.tokens[d]&&this.tokens[d].value===s&&this.tokens[d].type===y)return this.tokens[d]}matchToken(s,d){if(this.follows.indexOf(s)===-1&&(d=d||"IDENTIFIER",this.currentToken()&&this.currentToken().value===s&&this.currentToken().type===d))return this.consumeToken()}consumeToken(){var s=this.tokens.shift();return this.consumed.push(s),this._lastConsumed=s,this.consumeWhitespace(),s}consumeUntil(s,d){for(var y=[],x=this.token(0,!0);(d==null||x.type!==d)&&(s==null||x.value!==s)&&x.type!=="EOF";){var R=this.tokens.shift();this.consumed.push(R),y.push(x),x=this.token(0,!0)}return this.consumeWhitespace(),y}lastWhitespace(){return this.consumed[this.consumed.length-1]&&this.consumed[this.consumed.length-1].type==="WHITESPACE"?this.consumed[this.consumed.length-1].value:""}consumeUntilWhitespace(){return this.consumeUntil(null,"WHITESPACE")}hasMore(){return this.tokens.length>0}token(s,d){var y,x=0;do{if(!d)for(;this.tokens[x]&&this.tokens[x].type==="WHITESPACE";)x++;y=this.tokens[x],s--,x++}while(s>-1);return y||{type:"EOF",value:"<<<EOF>>>"}}currentToken(){return this.token(0)}lastMatch(){return this._lastConsumed}pushFollow(s){this.follows.push(s)}popFollow(){this.follows.pop()}clearFollows(){var s=this.follows;return this.follows=[],s}restoreFollows(s){this.follows=s}}de(u,"sourceFor",function(){return this.programSource.substring(this.startToken.start,this.endToken.end)}),de(u,"lineFor",function(){return this.programSource.split(`
`)[this.startToken.line-1]});class E{constructor(s){de(this,"GRAMMAR",{});de(this,"COMMANDS",{});de(this,"FEATURES",{});de(this,"LEAF_EXPRESSIONS",[]);de(this,"INDIRECT_EXPRESSIONS",[]);this.runtime=s,this.possessivesDisabled=!1,this.addGrammarElement("feature",function(d,y,x){if(x.matchOpToken("(")){var R=d.requireElement("feature",x);return x.requireOpToken(")"),R}var k=d.FEATURES[x.currentToken().value||""];if(k)return k(d,y,x)}),this.addGrammarElement("command",function(d,y,x){if(x.matchOpToken("(")){const w=d.requireElement("command",x);return x.requireOpToken(")"),w}var R=d.COMMANDS[x.currentToken().value||""];let k;return R?k=R(d,y,x):x.currentToken().type==="IDENTIFIER"&&(k=d.parseElement("pseudoCommand",x)),k&&d.parseElement("indirectStatement",x,k)}),this.addGrammarElement("commandList",function(d,y,x){if(x.hasMore()){var R=d.parseElement("command",x);if(R){x.matchToken("then");const k=d.parseElement("commandList",x);return k&&(R.next=k),R}}return{type:"emptyCommandListCommand",op:function(k){return y.findNext(this,k)},execute:function(k){return y.unifiedExec(this,k)}}}),this.addGrammarElement("leaf",function(d,y,x){var R=d.parseAnyOf(d.LEAF_EXPRESSIONS,x);return R??d.parseElement("symbol",x)}),this.addGrammarElement("indirectExpression",function(d,y,x,R){for(var k=0;k<d.INDIRECT_EXPRESSIONS.length;k++){var w=d.INDIRECT_EXPRESSIONS[k];R.endToken=x.lastMatch();var p=d.parseElement(w,x,R);if(p)return p}return R}),this.addGrammarElement("indirectStatement",function(d,y,x,R){if(x.matchToken("unless")){R.endToken=x.lastMatch();var k=d.requireElement("expression",x),w={type:"unlessStatementModifier",args:[k],op:function(p,q){return q?this.next:R},execute:function(p){return y.unifiedExec(this,p)}};return R.parent=w,w}return R}),this.addGrammarElement("primaryExpression",function(d,y,x){var R=d.parseElement("leaf",x);if(R)return d.parseElement("indirectExpression",x,R);d.raiseParseError(x,"Unexpected value: "+x.currentToken().value)})}use(s){return s(this),this}initElt(s,d,y){s.startToken=d,s.sourceFor=u.sourceFor,s.lineFor=u.lineFor,s.programSource=y.source}parseElement(s,d,y=void 0){var x=this.GRAMMAR[s];if(x){var R=d.currentToken(),k=x(this,this.runtime,d,y);if(k){this.initElt(k,R,d),k.endToken=k.endToken||d.lastMatch();for(var y=k.root;y!=null;)this.initElt(y,R,d),y=y.root}return k}}requireElement(s,d,y,x){var R=this.parseElement(s,d,x);return R||E.raiseParseError(d,y||"Expected "+s),R}parseAnyOf(s,d){for(var y=0;y<s.length;y++){var x=s[y],R=this.parseElement(x,d);if(R)return R}}addGrammarElement(s,d){this.GRAMMAR[s]=d}addCommand(s,d){var y=s+"Command",x=function(R,k,w){const p=d(R,k,w);if(p)return p.type=y,p.execute=function(q){return q.meta.command=p,k.unifiedExec(this,q)},p};this.GRAMMAR[y]=x,this.COMMANDS[s]=x}addFeature(s,d){var y=s+"Feature",x=function(R,k,w){var p=d(R,k,w);if(p)return p.isFeature=!0,p.keyword=s,p.type=y,p};this.GRAMMAR[y]=x,this.FEATURES[s]=x}addLeafExpression(s,d){this.LEAF_EXPRESSIONS.push(s),this.addGrammarElement(s,d)}addIndirectExpression(s,d){this.INDIRECT_EXPRESSIONS.push(s),this.addGrammarElement(s,d)}static createParserContext(s){var d=s.currentToken(),y=s.source,x=y.split(`
`),R=d&&d.line?d.line-1:x.length-1,k=x[R],w=d&&d.line?d.column:k.length-1;return k+`
`+" ".repeat(w)+`^^

`}static raiseParseError(s,d){d=(d||"Unexpected Token : "+s.currentToken().value)+`

`+E.createParserContext(s);var y=new Error(d);throw y.tokens=s,y}raiseParseError(s,d){E.raiseParseError(s,d)}parseHyperScript(s){var d=this.parseElement("hyperscript",s);if(s.hasMore()&&this.raiseParseError(s),d)return d}setParent(s,d){typeof s=="object"&&(s.parent=d,typeof d=="object"&&(d.children=d.children||new Set,d.children.add(s)),this.setParent(s.next,d))}commandStart(s){return this.COMMANDS[s.value||""]}featureStart(s){return this.FEATURES[s.value||""]}commandBoundary(s){return!!(s.value=="end"||s.value=="then"||s.value=="else"||s.value=="otherwise"||s.value==")"||this.commandStart(s)||this.featureStart(s)||s.type=="EOF")}parseStringTemplate(s){var d=[""];do if(d.push(s.lastWhitespace()),s.currentToken().value==="$"){s.consumeToken();var y=s.matchOpToken("{");d.push(this.requireElement("expression",s)),y&&s.requireOpToken("}"),d.push("")}else if(s.currentToken().value==="\\")s.consumeToken(),s.consumeToken();else{var x=s.consumeToken();d[d.length-1]+=x?x.value:""}while(s.hasMore());return d.push(s.lastWhitespace()),d}ensureTerminated(s){const d=this.runtime;for(var y={type:"implicitReturn",op:function(R){return R.meta.returned=!0,R.meta.resolve&&R.meta.resolve(),d.HALT},execute:function(R){}},x=s;x.next;)x=x.next;x.next=y}}const he=class he{constructor(s,d){de(this,"HALT",he.HALT);de(this,"_scriptAttrs",null);de(this,"hyperscriptFeaturesMap",new WeakMap);de(this,"internalDataMap",new WeakMap);de(this,"hyperscriptUrl","document"in n&&document.currentScript?document.currentScript.src:null);this.lexer=s??new o,this.parser=d??new E(this).use(se).use(ge),this.parser.runtime=this}matchesSelector(s,d){var y=s.matches||s.matchesSelector||s.msMatchesSelector||s.mozMatchesSelector||s.webkitMatchesSelector||s.oMatchesSelector;return y&&y.call(s,d)}makeEvent(s,d){var y;return n.Event&&typeof n.Event=="function"?(y=new Event(s,{bubbles:!0,cancelable:!0,composed:!0}),y.detail=d):(y=document.createEvent("CustomEvent"),y.initCustomEvent(s,!0,!0,d)),y}triggerEvent(s,d,y,x){y=y||{},y.sender=x;var R=this.makeEvent(d,y),k=s.dispatchEvent(R);return k}isArrayLike(s){return Array.isArray(s)||typeof NodeList<"u"&&(s instanceof NodeList||s instanceof HTMLCollection||s instanceof FileList)}isIterable(s){return typeof s=="object"&&Symbol.iterator in s&&typeof s[Symbol.iterator]=="function"}shouldAutoIterate(s){return s!=null&&s[ce]||this.isArrayLike(s)}forEach(s,d){if(s!=null)if(this.isIterable(s))for(const x of s)d(x);else if(this.isArrayLike(s))for(var y=0;y<s.length;y++)d(s[y]);else d(s)}implicitLoop(s,d){if(this.shouldAutoIterate(s))for(const y of s)d(y);else d(s)}wrapArrays(s){for(var d=[],y=0;y<s.length;y++){var x=s[y];Array.isArray(x)?d.push(Promise.all(x)):d.push(x)}return d}unwrapAsyncs(s){for(var d=0;d<s.length;d++){var y=s[d];if(y.asyncWrapper&&(s[d]=y.value),Array.isArray(y))for(var x=0;x<y.length;x++){var R=y[x];R.asyncWrapper&&(y[x]=R.value)}}}unifiedExec(s,d){for(;;){try{var y=this.unifiedEval(s,d)}catch(x){if(d.meta.handlingFinally)console.error(" Exception in finally block: ",x),y=he.HALT;else if(this.registerHyperTrace(d,x),d.meta.errorHandler&&!d.meta.handlingError){d.meta.handlingError=!0,d.locals[d.meta.errorSymbol]=x,s=d.meta.errorHandler;continue}else d.meta.currentException=x,y=he.HALT}if(y==null){console.error(s," did not return a next element to execute! context: ",d);return}else if(y.then){y.then(x=>{this.unifiedExec(x,d)}).catch(x=>{this.unifiedExec({op:function(){throw x}},d)});return}else if(y===he.HALT)if(d.meta.finallyHandler&&!d.meta.handlingFinally)d.meta.handlingFinally=!0,s=d.meta.finallyHandler;else if(d.meta.onHalt&&d.meta.onHalt(),d.meta.currentException)if(d.meta.reject){d.meta.reject(d.meta.currentException);return}else throw d.meta.currentException;else return;else s=y}}unifiedEval(s,d,y){var x=[d],R=!1,k=!1;if(s.args)for(var w=0;w<s.args.length;w++){var p=s.args[w];if(p==null)x.push(null);else if(Array.isArray(p)){for(var q=[],D=0;D<p.length;D++){var H=p[D],I=H?H.evaluate(d):null;I&&(I.then?R=!0:I.asyncWrapper&&(k=!0)),q.push(I)}x.push(q)}else if(p.evaluate){var I=p.evaluate(d);if(I&&(I.then?R=!0:I.asyncWrapper&&(k=!0)),x.push(I),I){if(y===!0)break}else if(y===!1)break}else x.push(p)}return R?new Promise((L,F)=>{x=this.wrapArrays(x),Promise.all(x).then(function(B){k&&this.unwrapAsyncs(B);try{var K=s.op.apply(s,B);L(K)}catch(Y){F(Y)}}).catch(function(B){F(B)})}):(k&&this.unwrapAsyncs(x),s.op.apply(s,x))}getScriptAttributes(){return this._scriptAttrs==null&&(this._scriptAttrs=a.attributes.replace(/ /g,"").split(",")),this._scriptAttrs}getScript(s){for(var d=0;d<this.getScriptAttributes().length;d++){var y=this.getScriptAttributes()[d];if(s.hasAttribute&&s.hasAttribute(y))return s.getAttribute(y)}return s instanceof HTMLScriptElement&&s.type==="text/hyperscript"?s.innerText:null}getHyperscriptFeatures(s){var d=this.hyperscriptFeaturesMap.get(s);return typeof d>"u"&&s&&this.hyperscriptFeaturesMap.set(s,d={}),d}addFeatures(s,d){s&&(Object.assign(d.locals,this.getHyperscriptFeatures(s)),this.addFeatures(s.parentElement,d))}makeContext(s,d,y,x){return new J(s,d,y,x,this)}getScriptSelector(){return this.getScriptAttributes().map(function(s){return"["+s+"]"}).join(", ")}convertValue(s,d){for(var y=r.dynamicResolvers,x=0;x<y.length;x++){var R=y[x],k=R(d,s);if(k!==void 0)return k}if(s==null)return null;var w=r[d];if(w)return w(s);throw"Unknown conversion : "+d}parse(s){const d=this.lexer,y=this.parser;var x=d.tokenize(s);if(this.parser.commandStart(x.currentToken())){var R=y.requireElement("commandList",x);return x.hasMore()&&y.raiseParseError(x),y.ensureTerminated(R),R}else if(y.featureStart(x.currentToken())){var k=y.requireElement("hyperscript",x);return x.hasMore()&&y.raiseParseError(x),k}else{var w=y.requireElement("expression",x);return x.hasMore()&&y.raiseParseError(x),w}}evaluateNoPromise(s,d){let y=s.evaluate(d);if(y.next)throw new Error(u.sourceFor.call(s)+" returned a Promise in a context that they are not allowed.");return y}evaluate(s,d,y){class x extends EventTarget{constructor(p){super(),this.module=p}toString(){return this.module.id}}var R="document"in n?n.document.body:new x(y&&y.module);d=Object.assign(this.makeContext(R,null,R,null),d||{});var k=this.parse(s);return k.execute?(k.execute(d),typeof d.meta.returnValue<"u"?d.meta.returnValue:d.result):k.apply?(k.apply(R,R,y),this.getHyperscriptFeatures(R)):k.evaluate(d)}processNode(s){var d=this.getScriptSelector();this.matchesSelector(s,d)&&this.initElement(s,s),s instanceof HTMLScriptElement&&s.type==="text/hyperscript"&&this.initElement(s,document.body),s.querySelectorAll&&this.forEach(s.querySelectorAll(d+", [type='text/hyperscript']"),y=>{this.initElement(y,y instanceof HTMLScriptElement&&y.type==="text/hyperscript"?document.body:y)})}initElement(s,d){if(!(s.closest&&s.closest(a.disableSelector))){var y=this.getInternalData(s);if(!y.initialized){var x=this.getScript(s);if(x)try{y.initialized=!0,y.script=x;const w=this.lexer,p=this.parser;var R=w.tokenize(x),k=p.parseHyperScript(R);if(!k)return;k.apply(d||s,s),setTimeout(()=>{this.triggerEvent(d||s,"load",{hyperscript:!0})},1)}catch(w){this.triggerEvent(s,"exception",{error:w}),console.error("hyperscript errors were found on the following element:",s,`

`,w.message,w.stack)}}}}getInternalData(s){var d=this.internalDataMap.get(s);return typeof d>"u"&&this.internalDataMap.set(s,d={}),d}typeCheck(s,d,y){if(s==null&&y)return!0;var x=Object.prototype.toString.call(s).slice(8,-1);return x===d}getElementScope(s){var d=s.meta&&s.meta.owner;if(d){var y=this.getInternalData(d),x="elementScope";s.meta.feature&&s.meta.feature.behavior&&(x=s.meta.feature.behavior+"Scope");var R=W(y,x);return R}else return{}}isReservedWord(s){return["meta","it","result","locals","event","target","detail","sender","body"].includes(s)}isHyperscriptContext(s){return s instanceof J}resolveSymbol(s,d,y){if(s==="me"||s==="my"||s==="I")return d.me;if(s==="it"||s==="its"||s==="result")return d.result;if(s==="you"||s==="your"||s==="yourself")return d.you;if(y==="global")return n[s];if(y==="element"){var x=this.getElementScope(d);return x[s]}else{if(y==="local")return d.locals[s];if(d.meta&&d.meta.context){var R=d.meta.context[s];if(typeof R<"u"||d.meta.context.detail&&(R=d.meta.context.detail[s],typeof R<"u"))return R}if(this.isHyperscriptContext(d)&&!this.isReservedWord(s))var k=d.locals[s];else var k=d[s];if(typeof k<"u")return k;var x=this.getElementScope(d);return k=x[s],typeof k<"u"?k:n[s]}}setSymbol(s,d,y,x){if(y==="global")n[s]=x;else if(y==="element"){var R=this.getElementScope(d);R[s]=x}else if(y==="local")d.locals[s]=x;else if(this.isHyperscriptContext(d)&&!this.isReservedWord(s)&&typeof d.locals[s]<"u")d.locals[s]=x;else{var R=this.getElementScope(d),k=R[s];typeof k<"u"?R[s]=x:this.isHyperscriptContext(d)&&!this.isReservedWord(s)?d.locals[s]=x:d[s]=x}}findNext(s,d){if(s)return s.resolveNext?s.resolveNext(d):s.next?s.next:this.findNext(s.parent,d)}flatGet(s,d,y){if(s!=null){var x=y(s,d);if(typeof x<"u")return x;if(this.shouldAutoIterate(s)){var R=[];for(var k of s){var w=y(k,d);R.push(w)}return R}}}resolveProperty(s,d){return this.flatGet(s,d,(y,x)=>y[x])}resolveAttribute(s,d){return this.flatGet(s,d,(y,x)=>y.getAttribute&&y.getAttribute(x))}resolveStyle(s,d){return this.flatGet(s,d,(y,x)=>y.style&&y.style[x])}resolveComputedStyle(s,d){return this.flatGet(s,d,(y,x)=>getComputedStyle(y).getPropertyValue(x))}assignToNamespace(s,d,y,x){let R;typeof document<"u"&&s===document.body?R=n:R=this.getHyperscriptFeatures(s);for(var k;(k=d.shift())!==void 0;){var w=R[k];w==null&&(w={},R[k]=w),R=w}R[y]=x}getHyperTrace(s,d){for(var y=[],x=s;x.meta.caller;)x=x.meta.caller;if(x.meta.traceMap)return x.meta.traceMap.get(d,y)}registerHyperTrace(s,d){for(var y=[],x=null;s!=null;)y.push(s),x=s,s=s.meta.caller;if(x.meta.traceMap==null&&(x.meta.traceMap=new Map),!x.meta.traceMap.get(d)){var R={trace:y,print:function(k){k=k||console.error,k("hypertrace /// ");for(var w=0,p=0;p<y.length;p++)w=Math.max(w,y[p].meta.feature.displayName.length);for(var p=0;p<y.length;p++){var q=y[p];k("  ->",q.meta.feature.displayName.padEnd(w+2),"-",q.meta.owner)}}};x.meta.traceMap.set(d,R)}}escapeSelector(s){return s.replace(/[:&()\[\]\/]/g,function(d){return"\\"+d})}nullCheck(s,d){if(s==null)throw new Error("'"+d.sourceFor()+"' is null")}isEmpty(s){return s==null||s.length===0}doesExist(s){if(s==null)return!1;if(this.shouldAutoIterate(s)){for(const d of s)return!0;return!1}return!0}getRootNode(s){if(s&&s instanceof Node){var d=s.getRootNode();if(d instanceof Document||d instanceof ShadowRoot)return d}return document}getEventQueueFor(s,d){let y=this.getInternalData(s);var x=y.eventQueues;x==null&&(x=new Map,y.eventQueues=x);var R=x.get(d);return R==null&&(R={queue:[],executing:!1},x.set(d,R)),R}beepValueToConsole(s,d,y){if(this.triggerEvent(s,"hyperscript:beep",{element:s,expression:d,value:y})){var x;y?y instanceof re?x="ElementCollection":y.constructor?x=y.constructor.name:x="unknown":x="object (null)";var R=y;x==="String"?R='"'+R+'"':y instanceof re&&(R=Array.from(y)),console.log("///_ BEEP! The expression ("+u.sourceFor.call(d).replace("beep! ","")+") evaluates to:",R,"of type "+x)}}};de(he,"HALT",{});let b=he;function _(){return document.cookie.split("; ").map(s=>{let d=s.split("=");return{name:d[0],value:decodeURIComponent(d[1])}})}function P(C){document.cookie=C+"=;expires=Thu, 01 Jan 1970 00:00:00 GMT"}function j(){for(const C of _())P(C.name)}const $=new Proxy({},{get(C,s){var d;if(s==="then"||s==="asyncWrapper")return null;if(s==="length")return _().length;if(s==="clear")return P;if(s==="clearAll")return j;if(typeof s=="string")if(isNaN(s)){let y=(d=document.cookie.split("; ").find(x=>x.startsWith(s+"=")))==null?void 0:d.split("=")[1];if(y)return decodeURIComponent(y)}else return _()[parseInt(s)];else if(s===Symbol.iterator)return _()[s]},set(C,s,d){var y=null;return typeof d=="string"?(y=encodeURIComponent(d),y+=";samesite=lax"):(y=encodeURIComponent(d.value),d.expires&&(y+=";expires="+d.maxAge),d.maxAge&&(y+=";max-age="+d.maxAge),d.partitioned&&(y+=";partitioned="+d.partitioned),d.path&&(y+=";path="+d.path),d.samesite&&(y+=";samesite="+d.path),d.secure&&(y+=";secure="+d.path)),document.cookie=s+"="+y,!0}});class J{constructor(s,d,y,x,R){this.meta={parser:R.parser,lexer:R.lexer,runtime:R,owner:s,feature:d,iterators:{},ctx:this},this.locals={cookies:$},this.me=y,this.you=void 0,this.result=void 0,this.event=x,this.target=x?x.target:null,this.detail=x?x.detail:null,this.sender=x&&x.detail?x.detail.sender:null,this.body="document"in n?document.body:null,R.addFeatures(s,this)}}class re{constructor(s,d,y){this._css=s,this.relativeToElement=d,this.escape=y,this[ce]=!0}get css(){return this.escape?b.prototype.escapeSelector(this._css):this._css}get className(){return this._css.substr(1)}get id(){return this.className()}contains(s){for(let d of this)if(d.contains(s))return!0;return!1}get length(){return this.selectMatches().length}[Symbol.iterator](){return this.selectMatches()[Symbol.iterator]()}selectMatches(){return b.prototype.getRootNode(this.relativeToElement).querySelectorAll(this.css)}}const ce=Symbol();function W(C,s){var d=C[s];if(d)return d;var y={};return C[s]=y,y}function Q(C){try{return JSON.parse(C)}catch(s){return te(s),null}}function te(C){console.error?console.error(C):console.log&&console.log("ERROR: ",C)}function ee(C,s){return new(C.bind.apply(C,[C].concat(s)))}function se(C){C.addLeafExpression("parenthesized",function(c,f,i){if(i.matchOpToken("(")){var l=i.clearFollows();try{var v=c.requireElement("expression",i)}finally{i.restoreFollows(l)}return i.requireOpToken(")"),v}}),C.addLeafExpression("string",function(c,f,i){var l=i.matchTokenType("STRING");if(l){var v=l.value,m;if(l.template){var g=o.tokenize(v,!0);m=c.parseStringTemplate(g)}else m=[];return{type:"string",token:l,args:m,op:function(h){for(var T="",S=1;S<arguments.length;S++){var A=arguments[S];A!==void 0&&(T+=A)}return T},evaluate:function(h){return m.length===0?v:f.unifiedEval(this,h)}}}}),C.addGrammarElement("nakedString",function(c,f,i){if(i.hasMore()){var l=i.consumeUntilWhitespace();return i.matchTokenType("WHITESPACE"),{type:"nakedString",tokens:l,evaluate:function(v){return l.map(function(m){return m.value}).join("")}}}}),C.addLeafExpression("number",function(c,f,i){var l=i.matchTokenType("NUMBER");if(l){var v=l,m=parseFloat(l.value);return{type:"number",value:m,numberToken:v,evaluate:function(){return m}}}}),C.addLeafExpression("idRef",function(c,f,i){var l=i.matchTokenType("ID_REF");if(l&&l.value)if(l.template){var v=l.value.substring(2),m=o.tokenize(v),g=c.requireElement("expression",m);return{type:"idRefTemplate",args:[g],op:function(h,T){return f.getRootNode(h.me).getElementById(T)},evaluate:function(h){return f.unifiedEval(this,h)}}}else{const h=l.value.substring(1);return{type:"idRef",css:l.value,value:h,evaluate:function(T){return f.getRootNode(T.me).getElementById(h)}}}}),C.addLeafExpression("classRef",function(c,f,i){var l=i.matchTokenType("CLASS_REF");if(l&&l.value)if(l.template){var v=l.value.substring(2),m=o.tokenize(v),g=c.requireElement("expression",m);return{type:"classRefTemplate",args:[g],op:function(h,T){return new re("."+T,h.me,!0)},evaluate:function(h){return f.unifiedEval(this,h)}}}else{const h=l.value;return{type:"classRef",css:h,evaluate:function(T){return new re(h,T.me,!0)}}}});class s extends re{constructor(f,i,l){super(f,i),this.templateParts=l,this.elements=l.filter(v=>v instanceof Element)}get css(){let f="",i=0;for(const l of this.templateParts)l instanceof Element?f+="[data-hs-query-id='"+i+++"']":f+=l;return f}[Symbol.iterator](){this.elements.forEach((i,l)=>i.dataset.hsQueryId=l);const f=super[Symbol.iterator]();return this.elements.forEach(i=>i.removeAttribute("data-hs-query-id")),f}}C.addLeafExpression("queryRef",function(c,f,i){var l=i.matchOpToken("<");if(l){var v=i.consumeUntil("/");i.requireOpToken("/"),i.requireOpToken(">");var m=v.map(function(S){return S.type==="STRING"?'"'+S.value+'"':S.value}).join(""),g,h,T;return/\$[^=]/.test(m)&&(g=!0,h=o.tokenize(m,!0),T=c.parseStringTemplate(h)),{type:"queryRef",css:m,args:T,op:function(S,...A){return g?new s(m,S.me,A):new re(m,S.me)},evaluate:function(S){return f.unifiedEval(this,S)}}}}),C.addLeafExpression("attributeRef",function(c,f,i){var l=i.matchTokenType("ATTRIBUTE_REF");if(l&&l.value){var v=l.value;if(v.indexOf("[")===0)var m=v.substring(2,v.length-1);else var m=v.substring(1);var g="["+m+"]",h=m.split("="),T=h[0],S=h[1];return S&&S.indexOf('"')===0&&(S=S.substring(1,S.length-1)),{type:"attributeRef",name:T,css:g,value:S,op:function(A){var M=A.you||A.me;if(M)return M.getAttribute(T)},evaluate:function(A){return f.unifiedEval(this,A)}}}}),C.addLeafExpression("styleRef",function(c,f,i){var l=i.matchTokenType("STYLE_REF");if(l&&l.value){var v=l.value.substr(1);return v.startsWith("computed-")?(v=v.substr(9),{type:"computedStyleRef",name:v,op:function(m){var g=m.you||m.me;if(g)return f.resolveComputedStyle(g,v)},evaluate:function(m){return f.unifiedEval(this,m)}}):{type:"styleRef",name:v,op:function(m){var g=m.you||m.me;if(g)return f.resolveStyle(g,v)},evaluate:function(m){return f.unifiedEval(this,m)}}}}),C.addGrammarElement("objectKey",function(c,f,i){var l;if(l=i.matchTokenType("STRING"))return{type:"objectKey",key:l.value,evaluate:function(){return l.value}};if(i.matchOpToken("[")){var v=c.parseElement("expression",i);return i.requireOpToken("]"),{type:"objectKey",expr:v,args:[v],op:function(g,h){return h},evaluate:function(g){return f.unifiedEval(this,g)}}}else{var m="";do l=i.matchTokenType("IDENTIFIER")||i.matchOpToken("-"),l&&(m+=l.value);while(l);return{type:"objectKey",key:m,evaluate:function(){return m}}}}),C.addLeafExpression("objectLiteral",function(c,f,i){if(i.matchOpToken("{")){var l=[],v=[];if(!i.matchOpToken("}")){do{var m=c.requireElement("objectKey",i);i.requireOpToken(":");var g=c.requireElement("expression",i);v.push(g),l.push(m)}while(i.matchOpToken(",")&&!i.peekToken("}",0,"R_BRACE"));i.requireOpToken("}")}return{type:"objectLiteral",args:[l,v],op:function(h,T,S){for(var A={},M=0;M<T.length;M++)A[T[M]]=S[M];return A},evaluate:function(h){return f.unifiedEval(this,h)}}}}),C.addGrammarElement("nakedNamedArgumentList",function(c,f,i){var l=[],v=[];if(i.currentToken().type==="IDENTIFIER")do{var m=i.requireTokenType("IDENTIFIER");i.requireOpToken(":");var g=c.requireElement("expression",i);v.push(g),l.push({name:m,value:g})}while(i.matchOpToken(","));return{type:"namedArgumentList",fields:l,args:[v],op:function(h,T){for(var S={_namedArgList_:!0},A=0;A<T.length;A++){var M=l[A];S[M.name.value]=T[A]}return S},evaluate:function(h){return f.unifiedEval(this,h)}}}),C.addGrammarElement("namedArgumentList",function(c,f,i){if(i.matchOpToken("(")){var l=c.requireElement("nakedNamedArgumentList",i);return i.requireOpToken(")"),l}}),C.addGrammarElement("symbol",function(c,f,i){var l="default";i.matchToken("global")?l="global":i.matchToken("element")||i.matchToken("module")?(l="element",i.matchOpToken("'")&&i.requireToken("s")):i.matchToken("local")&&(l="local");let v=i.matchOpToken(":"),m=i.matchTokenType("IDENTIFIER");if(m&&m.value){var g=m.value;return v&&(g=":"+g),l==="default"&&(g.indexOf("$")===0&&(l="global"),g.indexOf(":")===0&&(l="element")),{type:"symbol",token:m,scope:l,name:g,evaluate:function(h){return f.resolveSymbol(g,h,l)}}}}),C.addGrammarElement("implicitMeTarget",function(c,f,i){return{type:"implicitMeTarget",evaluate:function(l){return l.you||l.me}}}),C.addLeafExpression("boolean",function(c,f,i){var l=i.matchToken("true")||i.matchToken("false");if(!l)return;const v=l.value==="true";return{type:"boolean",evaluate:function(m){return v}}}),C.addLeafExpression("null",function(c,f,i){if(i.matchToken("null"))return{type:"null",evaluate:function(l){return null}}}),C.addLeafExpression("arrayLiteral",function(c,f,i){if(i.matchOpToken("[")){var l=[];if(!i.matchOpToken("]")){do{var v=c.requireElement("expression",i);l.push(v)}while(i.matchOpToken(","));i.requireOpToken("]")}return{type:"arrayLiteral",values:l,args:[l],op:function(m,g){return g},evaluate:function(m){return f.unifiedEval(this,m)}}}}),C.addLeafExpression("blockLiteral",function(c,f,i){if(i.matchOpToken("\\")){var l=[],v=i.matchTokenType("IDENTIFIER");if(v)for(l.push(v);i.matchOpToken(",");)l.push(i.requireTokenType("IDENTIFIER"));i.requireOpToken("-"),i.requireOpToken(">");var m=c.requireElement("expression",i);return{type:"blockLiteral",args:l,expr:m,evaluate:function(g){var h=function(){for(var T=0;T<l.length;T++)g.locals[l[T].value]=arguments[T];return m.evaluate(g)};return h}}}}),C.addIndirectExpression("propertyAccess",function(c,f,i,l){if(i.matchOpToken(".")){var v=i.requireTokenType("IDENTIFIER"),m={type:"propertyAccess",root:l,prop:v,args:[l],op:function(g,h){var T=f.resolveProperty(h,v.value);return T},evaluate:function(g){return f.unifiedEval(this,g)}};return c.parseElement("indirectExpression",i,m)}}),C.addIndirectExpression("of",function(c,f,i,l){if(i.matchToken("of")){for(var v=c.requireElement("unaryExpression",i),m=null,g=l;g.root;)m=g,g=g.root;g.type!=="symbol"&&g.type!=="attributeRef"&&g.type!=="styleRef"&&g.type!=="computedStyleRef"&&c.raiseParseError(i,"Cannot take a property of a non-symbol: "+g.type);var h=g.type==="attributeRef",T=g.type==="styleRef"||g.type==="computedStyleRef";if(h||T)var S=g;var A=g.name,M={type:"ofExpression",prop:g.token,root:v,attribute:S,expression:l,args:[v],op:function(V,O){return h?f.resolveAttribute(O,A):T?g.type==="computedStyleRef"?f.resolveComputedStyle(O,A):f.resolveStyle(O,A):f.resolveProperty(O,A)},evaluate:function(V){return f.unifiedEval(this,V)}};return g.type==="attributeRef"&&(M.attribute=g),m?(m.root=M,m.args=[M]):l=M,c.parseElement("indirectExpression",i,l)}}),C.addIndirectExpression("possessive",function(c,f,i,l){if(!c.possessivesDisabled){var v=i.matchOpToken("'");if(v||l.type==="symbol"&&(l.name==="my"||l.name==="its"||l.name==="your")&&(i.currentToken().type==="IDENTIFIER"||i.currentToken().type==="ATTRIBUTE_REF"||i.currentToken().type==="STYLE_REF")){v&&i.requireToken("s");var m,g,h;m=c.parseElement("attributeRef",i),m==null&&(g=c.parseElement("styleRef",i),g==null&&(h=i.requireTokenType("IDENTIFIER")));var T={type:"possessive",root:l,attribute:m||g,prop:h,args:[l],op:function(S,A){if(m)var M=f.resolveAttribute(A,m.name);else if(g){var M;g.type==="computedStyleRef"?M=f.resolveComputedStyle(A,g.name):M=f.resolveStyle(A,g.name)}else var M=f.resolveProperty(A,h.value);return M},evaluate:function(S){return f.unifiedEval(this,S)}};return c.parseElement("indirectExpression",i,T)}}}),C.addIndirectExpression("inExpression",function(c,f,i,l){if(i.matchToken("in")){var v=c.requireElement("unaryExpression",i),m={type:"inExpression",root:l,args:[l,v],op:function(g,h,T){var S=[];if(h.css)f.implicitLoop(T,function(M){for(var V=M.querySelectorAll(h.css),O=0;O<V.length;O++)S.push(V[O])});else if(h instanceof Element){var A=!1;if(f.implicitLoop(T,function(M){M.contains(h)&&(A=!0)}),A)return h}else f.implicitLoop(h,function(M){f.implicitLoop(T,function(V){M===V&&S.push(M)})});return S},evaluate:function(g){return f.unifiedEval(this,g)}};return c.parseElement("indirectExpression",i,m)}}),C.addIndirectExpression("asExpression",function(c,f,i,l){if(i.matchToken("as")){i.matchToken("a")||i.matchToken("an");var v=c.requireElement("dotOrColonPath",i).evaluate(),m={type:"asExpression",root:l,args:[l],op:function(g,h){return f.convertValue(h,v)},evaluate:function(g){return f.unifiedEval(this,g)}};return c.parseElement("indirectExpression",i,m)}}),C.addIndirectExpression("functionCall",function(c,f,i,l){if(i.matchOpToken("(")){var v=[];if(!i.matchOpToken(")")){do v.push(c.requireElement("expression",i));while(i.matchOpToken(","));i.requireOpToken(")")}if(l.root)var m={type:"functionCall",root:l,argExressions:v,args:[l.root,v],op:function(g,h,T){f.nullCheck(h,l.root);var S=h[l.prop.value];return f.nullCheck(S,l),S.hyperfunc&&T.push(g),S.apply(h,T)},evaluate:function(g){return f.unifiedEval(this,g)}};else var m={type:"functionCall",root:l,argExressions:v,args:[l,v],op:function(h,T,S){f.nullCheck(T,l),T.hyperfunc&&S.push(h);var A=T.apply(null,S);return A},evaluate:function(h){return f.unifiedEval(this,h)}};return c.parseElement("indirectExpression",i,m)}}),C.addIndirectExpression("attributeRefAccess",function(c,f,i,l){var v=c.parseElement("attributeRef",i);if(v){var m={type:"attributeRefAccess",root:l,attribute:v,args:[l],op:function(g,h){var T=f.resolveAttribute(h,v.name);return T},evaluate:function(g){return f.unifiedEval(this,g)}};return m}}),C.addIndirectExpression("arrayIndex",function(c,f,i,l){if(i.matchOpToken("[")){var v=!1,m=!1,g=null,h=null;if(i.matchOpToken(".."))v=!0,g=c.requireElement("expression",i);else if(g=c.requireElement("expression",i),i.matchOpToken("..")){m=!0;var T=i.currentToken();T.type!=="R_BRACKET"&&(h=c.parseElement("expression",i))}i.requireOpToken("]");var S={type:"arrayIndex",root:l,prop:g,firstIndex:g,secondIndex:h,args:[l,g,h],op:function(A,M,V,O){return M==null?null:v?(V<0&&(V=M.length+V),M.slice(0,V+1)):m?O!=null?(O<0&&(O=M.length+O),M.slice(V,O+1)):M.slice(V):M[V]},evaluate:function(A){return f.unifiedEval(this,A)}};return c.parseElement("indirectExpression",i,S)}});var d=["em","ex","cap","ch","ic","rem","lh","rlh","vw","vh","vi","vb","vmin","vmax","cm","mm","Q","pc","pt","px"];C.addGrammarElement("postfixExpression",function(c,f,i){var l=c.parseElement("negativeNumber",i);let v=i.matchAnyToken.apply(i,d)||i.matchOpToken("%");if(v)return{type:"stringPostfix",postfix:v.value,args:[l],op:function(T,S){return""+S+v.value},evaluate:function(T){return f.unifiedEval(this,T)}};var m=null;if(i.matchToken("s")||i.matchToken("seconds")?m=1e3:(i.matchToken("ms")||i.matchToken("milliseconds"))&&(m=1),m)return{type:"timeExpression",time:l,factor:m,args:[l],op:function(T,S){return S*m},evaluate:function(T){return f.unifiedEval(this,T)}};if(i.matchOpToken(":")){var g=i.requireTokenType("IDENTIFIER");if(!g.value)return;var h=!i.matchOpToken("!");return{type:"typeCheck",typeName:g,nullOk:h,args:[l],op:function(T,S){var A=f.typeCheck(S,this.typeName.value,h);if(A)return S;throw new Error("Typecheck failed!  Expected: "+g.value)},evaluate:function(T){return f.unifiedEval(this,T)}}}else return l}),C.addGrammarElement("logicalNot",function(c,f,i){if(i.matchToken("not")){var l=c.requireElement("unaryExpression",i);return{type:"logicalNot",root:l,args:[l],op:function(v,m){return!m},evaluate:function(v){return f.unifiedEval(this,v)}}}}),C.addGrammarElement("noExpression",function(c,f,i){if(i.matchToken("no")){var l=c.requireElement("unaryExpression",i);return{type:"noExpression",root:l,args:[l],op:function(v,m){return f.isEmpty(m)},evaluate:function(v){return f.unifiedEval(this,v)}}}}),C.addLeafExpression("some",function(c,f,i){if(i.matchToken("some")){var l=c.requireElement("expression",i);return{type:"noExpression",root:l,args:[l],op:function(v,m){return!f.isEmpty(m)},evaluate(v){return f.unifiedEval(this,v)}}}}),C.addGrammarElement("negativeNumber",function(c,f,i){if(i.matchOpToken("-")){var l=c.requireElement("negativeNumber",i);return{type:"negativeNumber",root:l,args:[l],op:function(v,m){return-1*m},evaluate:function(v){return f.unifiedEval(this,v)}}}else return c.requireElement("primaryExpression",i)}),C.addGrammarElement("unaryExpression",function(c,f,i){return i.matchToken("the"),c.parseAnyOf(["beepExpression","logicalNot","relativePositionalExpression","positionalExpression","noExpression","postfixExpression"],i)}),C.addGrammarElement("beepExpression",function(c,f,i){if(i.matchToken("beep!")){var l=c.parseElement("unaryExpression",i);if(l){l.booped=!0;var v=l.evaluate;return l.evaluate=function(m){let g=v.apply(l,arguments),h=m.me;return f.beepValueToConsole(h,l,g),g},l}}});var y=function(c,f,i,l){for(var v=f.querySelectorAll(i),m=0;m<v.length;m++){var g=v[m];if(g.compareDocumentPosition(c)===Node.DOCUMENT_POSITION_PRECEDING)return g}if(l)return v[0]},x=function(c,f,i,l){for(var v=f.querySelectorAll(i),m=v.length-1;m>=0;m--){var g=v[m];if(g.compareDocumentPosition(c)===Node.DOCUMENT_POSITION_FOLLOWING)return g}if(l)return v[v.length-1]},R=function(c,f,i,l){var v=[];b.prototype.forEach(f,function(T){(T.matches(i)||T===c)&&v.push(T)});for(var m=0;m<v.length-1;m++){var g=v[m];if(g===c)return v[m+1]}if(l){var h=v[0];if(h&&h.matches(i))return h}},k=function(c,f,i,l){return R(c,Array.from(f).reverse(),i,l)};C.addGrammarElement("relativePositionalExpression",function(c,f,i){var l=i.matchAnyToken("next","previous");if(l){var v=l.value==="next",m=c.parseElement("expression",i);if(i.matchToken("from")){i.pushFollow("in");try{var g=c.requireElement("unaryExpression",i)}finally{i.popFollow()}}else var g=c.requireElement("implicitMeTarget",i);var h=!1,T;if(i.matchToken("in")){h=!0;var S=c.requireElement("unaryExpression",i)}else i.matchToken("within")?T=c.requireElement("unaryExpression",i):T=document.body;var A=!1;return i.matchToken("with")&&(i.requireToken("wrapping"),A=!0),{type:"relativePositionalExpression",from:g,forwardSearch:v,inSearch:h,wrapping:A,inElt:S,withinElt:T,operator:l.value,args:[m,g,S,T],op:function(M,V,O,N,G){var U=V.css;if(U==null)throw"Expected a CSS value to be returned by "+u.sourceFor.apply(m);if(h){if(N)return v?R(O,N,U,A):k(O,N,U,A)}else if(G)return v?y(O,G,U,A):x(O,G,U,A)},evaluate:function(M){return f.unifiedEval(this,M)}}}}),C.addGrammarElement("positionalExpression",function(c,f,i){var l=i.matchAnyToken("first","last","random");if(!l)return;i.matchAnyToken("in","from","of");var v=c.requireElement("unaryExpression",i);const m=l.value;return{type:"positionalExpression",rhs:v,operator:l.value,args:[v],op:function(g,h){if(h&&!Array.isArray(h)&&(h.children?h=h.children:h=Array.from(h)),h){if(m==="first")return h[0];if(m==="last")return h[h.length-1];if(m==="random")return h[Math.floor(Math.random()*h.length)]}},evaluate:function(g){return f.unifiedEval(this,g)}}}),C.addGrammarElement("mathOperator",function(c,f,i){var l=c.parseElement("unaryExpression",i),v,m=null;for(v=i.matchAnyOpToken("+","-","*","/")||i.matchToken("mod");v;){m=m||v;var g=v.value;m.value!==g&&c.raiseParseError(i,"You must parenthesize math operations with different operators");var h=c.parseElement("unaryExpression",i);l={type:"mathOperator",lhs:l,rhs:h,operator:g,args:[l,h],op:function(T,S,A){if(g==="+")return S+A;if(g==="-")return S-A;if(g==="*")return S*A;if(g==="/")return S/A;if(g==="mod")return S%A},evaluate:function(T){return f.unifiedEval(this,T)}},v=i.matchAnyOpToken("+","-","*","/")||i.matchToken("mod")}return l}),C.addGrammarElement("mathExpression",function(c,f,i){return c.parseAnyOf(["mathOperator","unaryExpression"],i)});function w(c,f,i){if(f.contains)return f.contains(i);if(f.includes)return f.includes(i);throw Error("The value of "+c.sourceFor()+" does not have a contains or includes method on it")}function p(c,f,i){if(f.match)return!!f.match(i);if(f.matches)return f.matches(i);throw Error("The value of "+c.sourceFor()+" does not have a match or matches method on it")}C.addGrammarElement("comparisonOperator",function(c,f,i){var l=c.parseElement("mathExpression",i),v=i.matchAnyOpToken("<",">","<=",">=","==","===","!=","!=="),m=v?v.value:null,g=!0,h=!1;if(m==null&&(i.matchToken("is")||i.matchToken("am")?i.matchToken("not")?i.matchToken("in")?m="not in":i.matchToken("a")||i.matchToken("an")?(m="not a",h=!0):i.matchToken("empty")?(m="not empty",g=!1):(i.matchToken("really")?m="!==":m="!=",i.matchToken("equal")&&i.matchToken("to")):i.matchToken("in")?m="in":i.matchToken("a")||i.matchToken("an")?(m="a",h=!0):i.matchToken("empty")?(m="empty",g=!1):i.matchToken("less")?(i.requireToken("than"),i.matchToken("or")?(i.requireToken("equal"),i.requireToken("to"),m="<="):m="<"):i.matchToken("greater")?(i.requireToken("than"),i.matchToken("or")?(i.requireToken("equal"),i.requireToken("to"),m=">="):m=">"):(i.matchToken("really")?m="===":m="==",i.matchToken("equal")&&i.matchToken("to")):i.matchToken("equals")?m="==":i.matchToken("really")?(i.requireToken("equals"),m="==="):i.matchToken("exist")||i.matchToken("exists")?(m="exist",g=!1):i.matchToken("matches")||i.matchToken("match")?m="match":i.matchToken("contains")||i.matchToken("contain")?m="contain":i.matchToken("includes")||i.matchToken("include")?m="include":(i.matchToken("do")||i.matchToken("does"))&&(i.requireToken("not"),i.matchToken("matches")||i.matchToken("match")?m="not match":i.matchToken("contains")||i.matchToken("contain")?m="not contain":i.matchToken("exist")||i.matchToken("exist")?(m="not exist",g=!1):i.matchToken("include")?m="not include":c.raiseParseError(i,"Expected matches or contains"))),m){var T,S,A;h?(T=i.requireTokenType("IDENTIFIER"),S=!i.matchOpToken("!")):g&&(A=c.requireElement("mathExpression",i),(m==="match"||m==="not match")&&(A=A.css?A.css:A));var M=l;l={type:"comparisonOperator",operator:m,typeName:T,nullOk:S,lhs:l,rhs:A,args:[l,A],op:function(V,O,N){if(m==="==")return O==N;if(m==="!=")return O!=N;if(m==="===")return O===N;if(m==="!==")return O!==N;if(m==="match")return O!=null&&p(M,O,N);if(m==="not match")return O==null||!p(M,O,N);if(m==="in")return N!=null&&w(A,N,O);if(m==="not in")return N==null||!w(A,N,O);if(m==="contain")return O!=null&&w(M,O,N);if(m==="not contain")return O==null||!w(M,O,N);if(m==="include")return O!=null&&w(M,O,N);if(m==="not include")return O==null||!w(M,O,N);if(m==="===")return O===N;if(m==="!==")return O!==N;if(m==="<")return O<N;if(m===">")return O>N;if(m==="<=")return O<=N;if(m===">=")return O>=N;if(m==="empty")return f.isEmpty(O);if(m==="not empty")return!f.isEmpty(O);if(m==="exist")return f.doesExist(O);if(m==="not exist")return!f.doesExist(O);if(m==="a")return f.typeCheck(O,T.value,S);if(m==="not a")return!f.typeCheck(O,T.value,S);throw"Unknown comparison : "+m},evaluate:function(V){return f.unifiedEval(this,V)}}}return l}),C.addGrammarElement("comparisonExpression",function(c,f,i){return c.parseAnyOf(["comparisonOperator","mathExpression"],i)}),C.addGrammarElement("logicalOperator",function(c,f,i){var l=c.parseElement("comparisonExpression",i),v,m=null;for(v=i.matchToken("and")||i.matchToken("or");v;){m=m||v,m.value!==v.value&&c.raiseParseError(i,"You must parenthesize logical operations with different operators");var g=c.requireElement("comparisonExpression",i);const h=v.value;l={type:"logicalOperator",operator:h,lhs:l,rhs:g,args:[l,g],op:function(T,S,A){return h==="and"?S&&A:S||A},evaluate:function(T){return f.unifiedEval(this,T,h==="or")}},v=i.matchToken("and")||i.matchToken("or")}return l}),C.addGrammarElement("logicalExpression",function(c,f,i){return c.parseAnyOf(["logicalOperator","mathExpression"],i)}),C.addGrammarElement("asyncExpression",function(c,f,i){if(i.matchToken("async")){var l=c.requireElement("logicalExpression",i),v={type:"asyncExpression",value:l,evaluate:function(m){return{asyncWrapper:!0,value:this.value.evaluate(m)}}};return v}else return c.parseElement("logicalExpression",i)}),C.addGrammarElement("expression",function(c,f,i){return i.matchToken("the"),c.parseElement("asyncExpression",i)}),C.addGrammarElement("assignableExpression",function(c,f,i){i.matchToken("the");var l=c.parseElement("primaryExpression",i);return l&&(l.type==="symbol"||l.type==="ofExpression"||l.type==="propertyAccess"||l.type==="attributeRefAccess"||l.type==="attributeRef"||l.type==="styleRef"||l.type==="arrayIndex"||l.type==="possessive")||c.raiseParseError(i,"A target expression must be writable.  The expression type '"+(l&&l.type)+"' is not."),l}),C.addGrammarElement("hyperscript",function(c,f,i){var l=[];if(i.hasMore())for(;c.featureStart(i.currentToken())||i.currentToken().value==="(";){var v=c.requireElement("feature",i);l.push(v),i.matchToken("end")}return{type:"hyperscript",features:l,apply:function(m,g,h){for(const T of l)T.install(m,g,h)}}});var q=function(c){var f=[];if(c.token(0).value==="("&&(c.token(1).value===")"||c.token(2).value===","||c.token(2).value===")")){c.matchOpToken("(");do f.push(c.requireTokenType("IDENTIFIER"));while(c.matchOpToken(","));c.requireOpToken(")")}return f};C.addFeature("on",function(c,f,i){if(i.matchToken("on")){var l=!1;i.matchToken("every")&&(l=!0);var v=[],m=null;do{var g=c.requireElement("eventName",i,"Expected event name"),h=g.evaluate();m?m=m+" or "+h:m="on "+h;var T=q(i),S=null;i.matchOpToken("[")&&(S=c.requireElement("expression",i),i.requireOpToken("]"));var A,M,V;if(i.currentToken().type==="NUMBER"){var O=i.consumeToken();if(!O.value)return;if(A=parseInt(O.value),i.matchToken("to")){var N=i.consumeToken();if(!N.value)return;M=parseInt(N.value)}else i.matchToken("and")&&(V=!0,i.requireToken("on"))}var G,U;if(h==="intersection"){if(G={},i.matchToken("with")&&(G.with=c.requireElement("expression",i).evaluate()),i.matchToken("having"))do i.matchToken("margin")?G.rootMargin=c.requireElement("stringLike",i).evaluate():i.matchToken("threshold")?G.threshold=c.requireElement("expression",i).evaluate():c.raiseParseError(i,"Unknown intersection config specification");while(i.matchToken("and"))}else if(h==="mutation")if(U={},i.matchToken("of"))do if(i.matchToken("anything"))U.attributes=!0,U.subtree=!0,U.characterData=!0,U.childList=!0;else if(i.matchToken("childList"))U.childList=!0;else if(i.matchToken("attributes"))U.attributes=!0,U.attributeOldValue=!0;else if(i.matchToken("subtree"))U.subtree=!0;else if(i.matchToken("characterData"))U.characterData=!0,U.characterDataOldValue=!0;else if(i.currentToken().type==="ATTRIBUTE_REF"){var ne=i.consumeToken();U.attributeFilter==null&&(U.attributeFilter=[]),ne.value.indexOf("@")==0?U.attributeFilter.push(ne.value.substring(1)):c.raiseParseError(i,"Only shorthand attribute references are allowed here")}else c.raiseParseError(i,"Unknown mutation config specification");while(i.matchToken("or"));else U.attributes=!0,U.characterData=!0,U.childList=!0;var ie=null,Ee=!1;if(i.matchToken("from"))if(i.matchToken("elsewhere"))Ee=!0;else{i.pushFollow("or");try{ie=c.requireElement("expression",i)}finally{i.popFollow()}ie||c.raiseParseError(i,'Expected either target value or "elsewhere".')}if(ie===null&&Ee===!1&&i.matchToken("elsewhere")&&(Ee=!0),i.matchToken("in"))var xe=c.parseElement("unaryExpression",i);if(i.matchToken("debounced")){i.requireToken("at");var ae=c.requireElement("unaryExpression",i),pe=ae.evaluate({})}else if(i.matchToken("throttled")){i.requireToken("at");var ae=c.requireElement("unaryExpression",i),be=ae.evaluate({})}v.push({execCount:0,every:l,on:h,args:T,filter:S,from:ie,inExpr:xe,elsewhere:Ee,startCount:A,endCount:M,unbounded:V,debounceTime:pe,throttleTime:be,mutationSpec:U,intersectionSpec:G,debounced:void 0,lastExec:void 0})}while(i.matchToken("or"));var ve=!0;if(!l&&i.matchToken("queue"))if(i.matchToken("all"))var ve=!1;else if(i.matchToken("first"))var Te=!0;else if(i.matchToken("none"))var Ie=!0;else i.requireToken("last");var _e=c.requireElement("commandList",i);c.ensureTerminated(_e);var Fe,De;if(i.matchToken("catch")&&(Fe=i.requireTokenType("IDENTIFIER").value,De=c.requireElement("commandList",i),c.ensureTerminated(De)),i.matchToken("finally")){var He=c.requireElement("commandList",i);c.ensureTerminated(He)}var ke={displayName:m,events:v,start:_e,every:l,execCount:0,errorHandler:De,errorSymbol:Fe,execute:function(fe){let Ce=f.getEventQueueFor(fe.me,ke);if(Ce.executing&&l===!1){if(Ie||Te&&Ce.queue.length>0)return;ve&&(Ce.queue.length=0),Ce.queue.push(fe);return}ke.execCount++,Ce.executing=!0,fe.meta.onHalt=function(){Ce.executing=!1;var ye=Ce.queue.shift();ye&&setTimeout(function(){ke.execute(ye)},1)},fe.meta.reject=function(ye){console.error(ye.message?ye.message:ye);var Z=f.getHyperTrace(fe,ye);Z&&Z.print(),f.triggerEvent(fe.me,"exception",{error:ye})},_e.execute(fe)},install:function(fe,Ce){for(const Z of ke.events){var ye;Z.elsewhere?ye=[document]:Z.from?ye=Z.from.evaluate(f.makeContext(fe,ke,fe,null)):ye=[fe],f.implicitLoop(ye,function(we){var Ne=Z.on;if(we==null){console.warn("'%s' feature ignored because target does not exists:",m,fe);return}if(Z.mutationSpec&&(Ne="hyperscript:mutation",new MutationObserver(function(Oe,ue){ke.executing||f.triggerEvent(we,Ne,{mutationList:Oe,observer:ue})}).observe(we,Z.mutationSpec)),Z.intersectionSpec){Ne="hyperscript:intersection";const Le=new IntersectionObserver(function(Oe){for(const Pe of Oe){var ue={observer:Le};ue=Object.assign(ue,Pe),ue.intersecting=Pe.isIntersecting,f.triggerEvent(we,Ne,ue)}},Z.intersectionSpec);Le.observe(we)}var Be=we.addEventListener||we.on;Be.call(we,Ne,function Le(Oe){if(typeof Node<"u"&&fe instanceof Node&&we!==fe&&!fe.isConnected){we.removeEventListener(Ne,Le);return}var ue=f.makeContext(fe,ke,fe,Oe);if(!(Z.elsewhere&&fe.contains(Oe.target))){Z.from&&(ue.result=we);for(const Me of Z.args){let je=ue.event[Me.value];je!==void 0?ue.locals[Me.value]=je:"detail"in ue.event&&(ue.locals[Me.value]=ue.event.detail[Me.value])}if(ue.meta.errorHandler=De,ue.meta.errorSymbol=Fe,ue.meta.finallyHandler=He,Z.filter){var Pe=ue.meta.context;ue.meta.context=ue.event;try{var Ue=Z.filter.evaluate(ue);if(!Ue)return}finally{ue.meta.context=Pe}}if(Z.inExpr){for(var qe=Oe.target;;)if(qe.matches&&qe.matches(Z.inExpr.css)){ue.result=qe;break}else if(qe=qe.parentElement,qe==null)return}if(Z.execCount++,Z.startCount){if(Z.endCount){if(Z.execCount<Z.startCount||Z.execCount>Z.endCount)return}else if(Z.unbounded){if(Z.execCount<Z.startCount)return}else if(Z.execCount!==Z.startCount)return}if(Z.debounceTime){Z.debounced&&clearTimeout(Z.debounced),Z.debounced=setTimeout(function(){ke.execute(ue)},Z.debounceTime);return}if(Z.throttleTime){if(Z.lastExec&&Date.now()<Z.lastExec+Z.throttleTime)return;Z.lastExec=Date.now()}ke.execute(ue)}})})}}};return c.setParent(_e,ke),ke}}),C.addFeature("def",function(c,f,i){if(i.matchToken("def")){var l=c.requireElement("dotOrColonPath",i),v=l.evaluate(),m=v.split("."),g=m.pop(),h=[];if(i.matchOpToken("(")&&!i.matchOpToken(")")){do h.push(i.requireTokenType("IDENTIFIER"));while(i.matchOpToken(","));i.requireOpToken(")")}var T=c.requireElement("commandList",i),S,A;if(i.matchToken("catch")&&(S=i.requireTokenType("IDENTIFIER").value,A=c.parseElement("commandList",i)),i.matchToken("finally")){var M=c.requireElement("commandList",i);c.ensureTerminated(M)}var V={displayName:g+"("+h.map(function(O){return O.value}).join(", ")+")",name:g,args:h,start:T,errorHandler:A,errorSymbol:S,finallyHandler:M,install:function(O,N){var G=function(){var U=f.makeContext(N,V,O,null);U.meta.errorHandler=A,U.meta.errorSymbol=S,U.meta.finallyHandler=M;for(var ne=0;ne<h.length;ne++){var ie=h[ne],Ee=arguments[ne];ie&&(U.locals[ie.value]=Ee)}U.meta.caller=arguments[h.length],U.meta.caller&&(U.meta.callingCommand=U.meta.caller.meta.command);var xe,ae=null,pe=new Promise(function(be,ve){xe=be,ae=ve});return T.execute(U),U.meta.returned?U.meta.returnValue:(U.meta.resolve=xe,U.meta.reject=ae,pe)};G.hyperfunc=!0,G.hypername=v,f.assignToNamespace(O,m,g,G)}};return c.ensureTerminated(T),A&&c.ensureTerminated(A),c.setParent(T,V),V}}),C.addFeature("set",function(c,f,i){let l=c.parseElement("setCommand",i);if(l){l.target.scope!=="element"&&c.raiseParseError(i,"variables declared at the feature level must be element scoped.");let v={start:l,install:function(m,g){l&&l.execute(f.makeContext(m,v,m,null))}};return c.ensureTerminated(l),v}}),C.addFeature("init",function(c,f,i){if(i.matchToken("init")){var l=i.matchToken("immediately"),v=c.requireElement("commandList",i),m={start:v,install:function(g,h){let T=function(){v&&v.execute(f.makeContext(g,m,g,null))};l?T():setTimeout(T,0)}};return c.ensureTerminated(v),c.setParent(v,m),m}}),C.addFeature("worker",function(c,f,i){if(i.matchToken("worker")){c.raiseParseError(i,"In order to use the 'worker' feature, include the _hyperscript worker plugin. See https://hyperscript.org/features/worker/ for more info.");return}}),C.addFeature("behavior",function(c,f,i){if(i.matchToken("behavior")){var l=c.requireElement("dotOrColonPath",i).evaluate(),v=l.split("."),m=v.pop(),g=[];if(i.matchOpToken("(")&&!i.matchOpToken(")")){do g.push(i.requireTokenType("IDENTIFIER").value);while(i.matchOpToken(","));i.requireOpToken(")")}for(var h=c.requireElement("hyperscript",i),T=0;T<h.features.length;T++){var S=h.features[T];S.behavior=l}return{install:function(A,M){f.assignToNamespace(n.document&&n.document.body,v,m,function(V,O,N){for(var G=f.getInternalData(V),U=W(G,l+"Scope"),ne=0;ne<g.length;ne++)U[g[ne]]=N[g[ne]];h.apply(V,O)})}}}}),C.addFeature("install",function(c,f,i){if(i.matchToken("install")){var l=c.requireElement("dotOrColonPath",i).evaluate(),v=l.split("."),m=c.parseElement("namedArgumentList",i),g;return g={install:function(h,T){f.unifiedEval({args:[m],op:function(S,A){for(var M=n,V=0;V<v.length;V++)if(M=M[v[V]],typeof M!="object"&&typeof M!="function")throw new Error("No such behavior defined as "+l);if(!(M instanceof Function))throw new Error(l+" is not a behavior");M(h,T,A)}},f.makeContext(h,g,h,null))}}}}),C.addGrammarElement("jsBody",function(c,f,i){for(var l=i.currentToken().start,v=i.currentToken(),m=[],g="",h=!1;i.hasMore();){v=i.consumeToken();var T=i.token(0,!0);if(T.type==="IDENTIFIER"&&T.value==="end")break;h?v.type==="IDENTIFIER"||v.type==="NUMBER"?g+=v.value:(g!==""&&m.push(g),g="",h=!1):v.type==="IDENTIFIER"&&v.value==="function"&&(h=!0)}var S=v.end+1;return{type:"jsBody",exposedFunctionNames:m,jsSource:i.source.substring(l,S)}}),C.addFeature("js",function(c,f,i){if(i.matchToken("js")){var l=c.requireElement("jsBody",i),v=l.jsSource+`
return { `+l.exposedFunctionNames.map(function(g){return g+":"+g}).join(",")+" } ",m=new Function(v);return{jsSource:v,function:m,exposedFunctionNames:l.exposedFunctionNames,install:function(){Object.assign(n,m())}}}}),C.addCommand("js",function(c,f,i){if(i.matchToken("js")){var l=[];if(i.matchOpToken("(")&&!i.matchOpToken(")")){do{var v=i.requireTokenType("IDENTIFIER");l.push(v.value)}while(i.matchOpToken(","));i.requireOpToken(")")}var m=c.requireElement("jsBody",i);i.matchToken("end");var g=ee(Function,l.concat([m.jsSource])),h={jsSource:m.jsSource,function:g,inputs:l,op:function(T){var S=[];l.forEach(function(M){S.push(f.resolveSymbol(M,T,"default"))});var A=g.apply(n,S);return A&&typeof A.then=="function"?new Promise(function(M){A.then(function(V){T.result=V,M(f.findNext(this,T))})}):(T.result=A,f.findNext(this,T))}};return h}}),C.addCommand("async",function(c,f,i){if(i.matchToken("async")){if(i.matchToken("do")){for(var l=c.requireElement("commandList",i),v=l;v.next;)v=v.next;v.next=f.HALT,i.requireToken("end")}else var l=c.requireElement("command",i);var m={body:l,op:function(g){return setTimeout(function(){l.execute(g)}),f.findNext(this,g)}};return c.setParent(l,m),m}}),C.addCommand("tell",function(c,f,i){var l=i.currentToken();if(i.matchToken("tell")){var v=c.requireElement("expression",i),m=c.requireElement("commandList",i);i.hasMore()&&!c.featureStart(i.currentToken())&&i.requireToken("end");var g="tell_"+l.start,h={value:v,body:m,args:[v],resolveNext:function(T){var S=T.meta.iterators[g];return S.index<S.value.length?(T.you=S.value[S.index++],m):(T.you=S.originalYou,this.next?this.next:f.findNext(this.parent,T))},op:function(T,S){return S==null?S=[]:Array.isArray(S)||S instanceof NodeList||(S=[S]),T.meta.iterators[g]={originalYou:T.you,index:0,value:S},this.resolveNext(T)}};return c.setParent(m,h),h}}),C.addCommand("wait",function(c,f,i){if(i.matchToken("wait")){var l;if(i.matchToken("for")){i.matchToken("a");var v=[];do{var m=i.token(0);m.type==="NUMBER"||m.type==="L_PAREN"?v.push({time:c.requireElement("expression",i).evaluate()}):v.push({name:c.requireElement("dotOrColonPath",i,"Expected event name").evaluate(),args:q(i)})}while(i.matchToken("or"));if(i.matchToken("from"))var g=c.requireElement("expression",i);return l={event:v,on:g,args:[g],op:function(T,S){var A=S||T.me;if(!(A instanceof EventTarget))throw new Error("Not a valid event target: "+this.on.sourceFor());return new Promise(M=>{var V=!1;for(const N of v){var O=G=>{if(T.result=G,N.args)for(const U of N.args)T.locals[U.value]=G[U.value]||(G.detail?G.detail[U.value]:null);V||(V=!0,M(f.findNext(this,T)))};N.name?A.addEventListener(N.name,O,{once:!0}):N.time!=null&&setTimeout(O,N.time,N.time)}})}},l}else{var h;return i.matchToken("a")?(i.requireToken("tick"),h=0):h=c.requireElement("expression",i),l={type:"waitCmd",time:h,args:[h],op:function(T,S){return new Promise(A=>{setTimeout(()=>{A(f.findNext(this,T))},S)})},execute:function(T){return f.unifiedExec(this,T)}},l}}}),C.addGrammarElement("dotOrColonPath",function(c,f,i){var l=i.matchTokenType("IDENTIFIER");if(l){var v=[l.value],m=i.matchOpToken(".")||i.matchOpToken(":");if(m)do v.push(i.requireTokenType("IDENTIFIER","NUMBER").value);while(i.matchOpToken(m.value));return{type:"dotOrColonPath",path:v,evaluate:function(){return v.join(m?m.value:"")}}}}),C.addGrammarElement("eventName",function(c,f,i){var l;return(l=i.matchTokenType("STRING"))?{evaluate:function(){return l.value}}:c.parseElement("dotOrColonPath",i)});function D(c,f,i,l){var v=f.requireElement("eventName",l),m=f.parseElement("namedArgumentList",l);if(c==="send"&&l.matchToken("to")||c==="trigger"&&l.matchToken("on"))var g=f.requireElement("expression",l);else var g=f.requireElement("implicitMeTarget",l);var h={eventName:v,details:m,to:g,args:[g,v,m],op:function(T,S,A,M){return i.nullCheck(S,g),i.implicitLoop(S,function(V){i.triggerEvent(V,A,M,T.me)}),i.findNext(h,T)}};return h}C.addCommand("trigger",function(c,f,i){if(i.matchToken("trigger"))return D("trigger",c,f,i)}),C.addCommand("send",function(c,f,i){if(i.matchToken("send"))return D("send",c,f,i)});var H=function(c,f,i,l){if(l)if(c.commandBoundary(i.currentToken()))c.raiseParseError(i,"'return' commands must return a value.  If you do not wish to return a value, use 'exit' instead.");else var v=c.requireElement("expression",i);var m={value:v,args:[v],op:function(g,h){var T=g.meta.resolve;return g.meta.returned=!0,g.meta.returnValue=h,T&&(h?T(h):T()),f.HALT}};return m};C.addCommand("return",function(c,f,i){if(i.matchToken("return"))return H(c,f,i,!0)}),C.addCommand("exit",function(c,f,i){if(i.matchToken("exit"))return H(c,f,i,!1)}),C.addCommand("halt",function(c,f,i){if(i.matchToken("halt")){if(i.matchToken("the")){i.requireToken("event"),i.matchOpToken("'")&&i.requireToken("s");var l=!0}if(i.matchToken("bubbling"))var v=!0;else if(i.matchToken("default"))var m=!0;var g=H(c,f,i,!1),h={keepExecuting:!0,bubbling:v,haltDefault:m,exit:g,op:function(T){if(T.event)return v?T.event.stopPropagation():(m||T.event.stopPropagation(),T.event.preventDefault()),l?f.findNext(this,T):g}};return h}}),C.addCommand("log",function(c,f,i){if(i.matchToken("log")){for(var l=[c.parseElement("expression",i)];i.matchOpToken(",");)l.push(c.requireElement("expression",i));if(i.matchToken("with"))var v=c.requireElement("expression",i);var m={exprs:l,withExpr:v,args:[v,l],op:function(g,h,T){return h?h.apply(null,T):console.log.apply(null,T),f.findNext(this,g)}};return m}}),C.addCommand("beep!",function(c,f,i){if(i.matchToken("beep!")){for(var l=[c.parseElement("expression",i)];i.matchOpToken(",");)l.push(c.requireElement("expression",i));var v={exprs:l,args:[l],op:function(m,g){for(let h=0;h<l.length;h++){const T=l[h],S=g[h];f.beepValueToConsole(m.me,T,S)}return f.findNext(this,m)}};return v}}),C.addCommand("throw",function(c,f,i){if(i.matchToken("throw")){var l=c.requireElement("expression",i),v={expr:l,args:[l],op:function(m,g){throw f.registerHyperTrace(m,g),g}};return v}});var I=function(c,f,i){var l=c.requireElement("expression",i),v={expr:l,args:[l],op:function(m,g){return m.result=g,f.findNext(v,m)}};return v};C.addCommand("call",function(c,f,i){if(i.matchToken("call")){var l=I(c,f,i);return l.expr&&l.expr.type!=="functionCall"&&c.raiseParseError(i,"Must be a function invocation"),l}}),C.addCommand("get",function(c,f,i){if(i.matchToken("get"))return I(c,f,i)}),C.addCommand("make",function(c,f,i){if(i.matchToken("make")){i.matchToken("a")||i.matchToken("an");var l=c.requireElement("expression",i),v=[];if(l.type!=="queryRef"&&i.matchToken("from"))do v.push(c.requireElement("expression",i));while(i.matchOpToken(","));if(i.matchToken("called"))var m=c.requireElement("symbol",i);var g;return l.type==="queryRef"?(g={op:function(h){for(var T,S="div",A,M=[],V=/(?:(^|#|\.)([^#\. ]+))/g;T=V.exec(l.css);)T[1]===""?S=T[2].trim():T[1]==="#"?A=T[2].trim():M.push(T[2].trim());var O=document.createElement(S);A!==void 0&&(O.id=A);for(var N=0;N<M.length;N++){var G=M[N];O.classList.add(G)}return h.result=O,m&&f.setSymbol(m.name,h,m.scope,O),f.findNext(this,h)}},g):(g={args:[l,v],op:function(h,T,S){return h.result=ee(T,S),m&&f.setSymbol(m.name,h,m.scope,h.result),f.findNext(this,h)}},g)}}),C.addGrammarElement("pseudoCommand",function(c,f,i){let l=i.token(1);if(!(l&&l.op&&(l.value==="."||l.value==="(")))return null;for(var v=c.requireElement("primaryExpression",i),m=v.root,g=v;m.root!=null;)g=g.root,m=m.root;if(v.type!=="functionCall"&&c.raiseParseError(i,"Pseudo-commands must be function calls"),g.type==="functionCall"&&g.root.root==null){if(i.matchAnyToken("the","to","on","with","into","from","at"))var h=c.requireElement("expression",i);else if(i.matchToken("me"))var h=c.requireElement("implicitMeTarget",i)}var T;return h?T={type:"pseudoCommand",root:h,argExressions:g.argExressions,args:[h,g.argExressions],op:function(S,A,M){f.nullCheck(A,h);var V=A[g.root.name];return f.nullCheck(V,g),V.hyperfunc&&M.push(S),S.result=V.apply(A,M),f.findNext(T,S)},execute:function(S){return f.unifiedExec(this,S)}}:T={type:"pseudoCommand",expr:v,args:[v],op:function(S,A){return S.result=A,f.findNext(T,S)},execute:function(S){return f.unifiedExec(this,S)}},T});var L=function(c,f,i,l,v){var m=l.type==="symbol",g=l.type==="attributeRef",h=l.type==="styleRef",T=l.type==="arrayIndex";!(g||h||m)&&l.root==null&&c.raiseParseError(i,"Can only put directly into symbols, not references");var S=null,A=null;if(!m)if(g||h){S=c.requireElement("implicitMeTarget",i);var M=l}else if(T)A=l.firstIndex,S=l.root;else{A=l.prop?l.prop.value:null;var M=l.attribute;S=l.root}var V={target:l,symbolWrite:m,value:v,args:[S,A,v],op:function(O,N,G,U){return m?f.setSymbol(l.name,O,l.scope,U):(f.nullCheck(N,S),T?N[G]=U:f.implicitLoop(N,function(ne){M?M.type==="attributeRef"?U==null?ne.removeAttribute(M.name):ne.setAttribute(M.name,U):ne.style[M.name]=U:ne[G]=U})),f.findNext(this,O)}};return V};C.addCommand("default",function(c,f,i){if(i.matchToken("default")){var l=c.requireElement("assignableExpression",i);i.requireToken("to");var v=c.requireElement("expression",i),m=L(c,f,i,l,v),g={target:l,value:v,setter:m,args:[l],op:function(h,T){return T?f.findNext(this,h):m}};return m.parent=g,g}}),C.addCommand("set",function(c,f,i){if(i.matchToken("set")){if(i.currentToken().type==="L_BRACE"){var l=c.requireElement("objectLiteral",i);i.requireToken("on");var v=c.requireElement("expression",i),m={objectLiteral:l,target:v,args:[l,v],op:function(h,T,S){return Object.assign(S,T),f.findNext(this,h)}};return m}try{i.pushFollow("to");var v=c.requireElement("assignableExpression",i)}finally{i.popFollow()}i.requireToken("to");var g=c.requireElement("expression",i);return L(c,f,i,v,g)}}),C.addCommand("if",function(c,f,i){if(!i.matchToken("if"))return;var l=c.requireElement("expression",i);i.matchToken("then");var v=c.parseElement("commandList",i),m=!1;let g=i.matchToken("else")||i.matchToken("otherwise");if(g){let S=i.peekToken("if");if(m=S!=null&&S.line===g.line,m)var h=c.parseElement("command",i);else var h=c.parseElement("commandList",i)}i.hasMore()&&!m&&i.requireToken("end");var T={expr:l,trueBranch:v,falseBranch:h,args:[l],op:function(S,A){return A?v:h||f.findNext(this,S)}};return c.setParent(v,T),c.setParent(h,T),T});var F=function(c,f,i,l){var v=f.currentToken(),m;if(f.matchToken("for")||l){var g=f.requireTokenType("IDENTIFIER");m=g.value,f.requireToken("in");var h=c.requireElement("expression",f)}else if(f.matchToken("in")){m="it";var h=c.requireElement("expression",f)}else if(f.matchToken("while"))var T=c.requireElement("expression",f);else if(f.matchToken("until")){var S=!0;if(f.matchToken("event")){var A=c.requireElement("dotOrColonPath",f,"Expected event name");if(f.matchToken("from"))var M=c.requireElement("expression",f)}else var T=c.requireElement("expression",f)}else if(!c.commandBoundary(f.currentToken())&&f.currentToken().value!=="forever"){var V=c.requireElement("expression",f);f.requireToken("times")}else{f.matchToken("forever");var O=!0}if(f.matchToken("index"))var g=f.requireTokenType("IDENTIFIER"),N=g.value;else if(f.matchToken("indexed")){f.requireToken("by");var g=f.requireTokenType("IDENTIFIER"),N=g.value}var G=c.parseElement("commandList",f);if(G&&A){for(var U=G;U.next;)U=U.next;var ne={type:"waitATick",op:function(){return new Promise(function(ae){setTimeout(function(){ae(i.findNext(ne))},0)})}};U.next=ne}if(f.hasMore()&&f.requireToken("end"),m==null){m="_implicit_repeat_"+v.start;var ie=m}else var ie=m+"_"+v.start;var Ee={identifier:m,indexIdentifier:N,slot:ie,expression:h,forever:O,times:V,until:S,event:A,on:M,whileExpr:T,resolveNext:function(){return this},loop:G,args:[T,V],op:function(ae,pe,be){var ve=ae.meta.iterators[ie],Te=!1,Ie=null;if(this.forever)Te=!0;else if(this.until)A?Te=ae.meta.iterators[ie].eventFired===!1:Te=pe!==!0;else if(T)Te=pe;else if(be)Te=ve.index<be;else{var _e=ve.iterator.next();Te=!_e.done,Ie=_e.value}return Te?(ve.value?ae.result=ae.locals[m]=Ie:ae.result=ve.index,N&&(ae.locals[N]=ve.index),ve.index++,G):(ae.meta.iterators[ie]=null,i.findNext(this.parent,ae))}};c.setParent(G,Ee);var xe={name:"repeatInit",args:[h,A,M],op:function(ae,pe,be,ve){var Te={index:0,value:pe,eventFired:!1};if(ae.meta.iterators[ie]=Te,pe&&pe[Symbol.iterator]&&(Te.iterator=pe[Symbol.iterator]()),A){var Ie=ve||ae.me;Ie.addEventListener(be,function(_e){ae.meta.iterators[ie].eventFired=!0},{once:!0})}return Ee},execute:function(ae){return i.unifiedExec(this,ae)}};return c.setParent(Ee,xe),xe};C.addCommand("repeat",function(c,f,i){if(i.matchToken("repeat"))return F(c,i,f,!1)}),C.addCommand("for",function(c,f,i){if(i.matchToken("for"))return F(c,i,f,!0)}),C.addCommand("continue",function(c,f,i){if(i.matchToken("continue")){var l={op:function(v){for(var m=this.parent;;m=m.parent)if(m==null&&c.raiseParseError(i,"Command `continue` cannot be used outside of a `repeat` loop."),m.loop!=null)return m.resolveNext(v)}};return l}}),C.addCommand("break",function(c,f,i){if(i.matchToken("break")){var l={op:function(v){for(var m=this.parent;;m=m.parent)if(m==null&&c.raiseParseError(i,"Command `continue` cannot be used outside of a `repeat` loop."),m.loop!=null)return f.findNext(m.parent,v)}};return l}}),C.addGrammarElement("stringLike",function(c,f,i){return c.parseAnyOf(["string","nakedString"],i)}),C.addCommand("append",function(c,f,i){if(i.matchToken("append")){var l=null,v=c.requireElement("expression",i),m={type:"symbol",evaluate:function(T){return f.resolveSymbol("result",T)}};i.matchToken("to")?l=c.requireElement("expression",i):l=m;var g=null;(l.type==="symbol"||l.type==="attributeRef"||l.root!=null)&&(g=L(c,f,i,l,m));var h={value:v,target:l,args:[l,v],op:function(T,S,A){if(Array.isArray(S))return S.push(A),f.findNext(this,T);if(S instanceof Element)return A instanceof Element?S.insertAdjacentElement("beforeend",A):S.insertAdjacentHTML("beforeend",A),f.processNode(S),f.findNext(this,T);if(g)return T.result=(S||"")+A,g;throw Error("Unable to append a value!")},execute:function(T){return f.unifiedExec(this,T)}};return g!=null&&(g.parent=h),h}});function B(c,f,i){i.matchToken("at")||i.matchToken("from");const l={includeStart:!0,includeEnd:!1};return l.from=i.matchToken("start")?0:c.requireElement("expression",i),(i.matchToken("to")||i.matchOpToken(".."))&&(i.matchToken("end")?l.toEnd=!0:l.to=c.requireElement("expression",i)),i.matchToken("inclusive")?l.includeEnd=!0:i.matchToken("exclusive")&&(l.includeStart=!1),l}class K{constructor(f,i){this.re=f,this.str=i}next(){const f=this.re.exec(this.str);return f===null?{done:!0}:{value:f}}}class Y{constructor(f,i,l){this.re=f,this.flags=i,this.str=l}[Symbol.iterator](){return new K(new RegExp(this.re,this.flags),this.str)}}C.addCommand("pick",(c,f,i)=>{if(i.matchToken("pick")){if(i.matchToken("the"),i.matchToken("item")||i.matchToken("items")||i.matchToken("character")||i.matchToken("characters")){const l=B(c,f,i);return i.requireToken("from"),{args:[c.requireElement("expression",i),l.from,l.to],op(m,g,h,T){return l.toEnd&&(T=g.length),l.includeStart||h++,l.includeEnd&&T++,(T==null||T==null)&&(T=h+1),m.result=g.slice(h,T),f.findNext(this,m)}}}if(i.matchToken("match")){i.matchToken("of");const l=c.parseElement("expression",i);let v="";return i.matchOpToken("|")&&(v=i.requireTokenType("IDENTIFIER").value),i.requireToken("from"),{args:[c.parseElement("expression",i),l],op(g,h,T){return g.result=new RegExp(T,v).exec(h),f.findNext(this,g)}}}if(i.matchToken("matches")){i.matchToken("of");const l=c.parseElement("expression",i);let v="gu";return i.matchOpToken("|")&&(v="g"+i.requireTokenType("IDENTIFIER").value.replace("g","")),i.requireToken("from"),{args:[c.parseElement("expression",i),l],op(g,h,T){return g.result=new Y(T,v,h),f.findNext(this,g)}}}}}),C.addCommand("increment",function(c,f,i){if(i.matchToken("increment")){var l,v=c.parseElement("assignableExpression",i);i.matchToken("by")&&(l=c.requireElement("expression",i));var m={type:"implicitIncrementOp",target:v,args:[v,l],op:function(g,h,T){h=h?parseFloat(h):0,T=l?parseFloat(T):1;var S=h+T;return g.result=S,S},evaluate:function(g){return f.unifiedEval(this,g)}};return L(c,f,i,v,m)}}),C.addCommand("decrement",function(c,f,i){if(i.matchToken("decrement")){var l,v=c.parseElement("assignableExpression",i);i.matchToken("by")&&(l=c.requireElement("expression",i));var m={type:"implicitDecrementOp",target:v,args:[v,l],op:function(g,h,T){h=h?parseFloat(h):0,T=l?parseFloat(T):1;var S=h-T;return g.result=S,S},evaluate:function(g){return f.unifiedEval(this,g)}};return L(c,f,i,v,m)}});function X(c,f){var i="text",l;return c.matchToken("a")||c.matchToken("an"),c.matchToken("json")||c.matchToken("Object")?i="json":c.matchToken("response")?i="response":c.matchToken("html")?i="html":c.matchToken("text")||(l=f.requireElement("dotOrColonPath",c).evaluate()),{type:i,conversion:l}}C.addCommand("fetch",function(c,f,i){if(i.matchToken("fetch")){var l=c.requireElement("stringLike",i);if(i.matchToken("as"))var v=X(i,c);if(i.matchToken("with")&&i.currentToken().value!=="{")var m=c.parseElement("nakedNamedArgumentList",i);else var m=c.parseElement("objectLiteral",i);v==null&&i.matchToken("as")&&(v=X(i,c));var g=v?v.type:"text",h=v?v.conversion:null,T={url:l,argExpressions:m,args:[l,m],op:function(S,A,M){var V=M||{};V.sender=S.me,V.headers=V.headers||{};var O=new AbortController;let N=S.me.addEventListener("fetch:abort",function(){O.abort()},{once:!0});V.signal=O.signal,f.triggerEvent(S.me,"hyperscript:beforeFetch",V),f.triggerEvent(S.me,"fetch:beforeRequest",V),M=V;var G=!1;return M.timeout&&setTimeout(function(){G||O.abort()},M.timeout),fetch(A,M).then(function(U){let ne={response:U};return f.triggerEvent(S.me,"fetch:afterResponse",ne),U=ne.response,g==="response"?(S.result=U,f.triggerEvent(S.me,"fetch:afterRequest",{result:U}),G=!0,f.findNext(T,S)):g==="json"?U.json().then(function(ie){return S.result=ie,f.triggerEvent(S.me,"fetch:afterRequest",{result:ie}),G=!0,f.findNext(T,S)}):U.text().then(function(ie){return h&&(ie=f.convertValue(ie,h)),g==="html"&&(ie=f.convertValue(ie,"Fragment")),S.result=ie,f.triggerEvent(S.me,"fetch:afterRequest",{result:ie}),G=!0,f.findNext(T,S)})}).catch(function(U){throw f.triggerEvent(S.me,"fetch:error",{reason:U}),U}).finally(function(){S.me.removeEventListener("fetch:abort",N)})}};return T}})}function ge(C){C.addCommand("settle",function(k,w,p){if(p.matchToken("settle")){if(k.commandBoundary(p.currentToken()))var q=k.requireElement("implicitMeTarget",p);else var q=k.requireElement("expression",p);var D={type:"settleCmd",args:[q],op:function(H,I){w.nullCheck(I,q);var L=null,F=!1,B=new Promise(function(K){L=K});return I.addEventListener("transitionstart",function(){F=!0},{once:!0}),setTimeout(function(){F||L(w.findNext(D,H))},500),I.addEventListener("transitionend",function(){L(w.findNext(D,H))},{once:!0}),B},execute:function(H){return w.unifiedExec(this,H)}};return D}}),C.addCommand("add",function(k,w,p){if(p.matchToken("add")){var q=k.parseElement("classRef",p),D=null,H=null;if(q==null)D=k.parseElement("attributeRef",p),D==null&&(H=k.parseElement("styleLiteral",p),H==null&&k.raiseParseError(p,"Expected either a class reference or attribute expression"));else for(var I=[q];q=k.parseElement("classRef",p);)I.push(q);if(p.matchToken("to"))var L=k.requireElement("expression",p);else var L=k.requireElement("implicitMeTarget",p);if(p.matchToken("when")){H&&k.raiseParseError(p,"Only class and properties are supported with a when clause");var F=k.requireElement("expression",p)}return I?{classRefs:I,to:L,args:[L,I],op:function(B,K,Y){return w.nullCheck(K,L),w.forEach(Y,function(X){w.implicitLoop(K,function(c){F?(B.result=c,w.evaluateNoPromise(F,B)?c instanceof Element&&c.classList.add(X.className):c instanceof Element&&c.classList.remove(X.className),B.result=null):c instanceof Element&&c.classList.add(X.className)})}),w.findNext(this,B)}}:D?{type:"addCmd",attributeRef:D,to:L,args:[L],op:function(B,K,Y){return w.nullCheck(K,L),w.implicitLoop(K,function(X){F?(B.result=X,w.evaluateNoPromise(F,B)?X.setAttribute(D.name,D.value):X.removeAttribute(D.name),B.result=null):X.setAttribute(D.name,D.value)}),w.findNext(this,B)},execute:function(B){return w.unifiedExec(this,B)}}:{type:"addCmd",cssDeclaration:H,to:L,args:[L,H],op:function(B,K,Y){return w.nullCheck(K,L),w.implicitLoop(K,function(X){X.style.cssText+=Y}),w.findNext(this,B)},execute:function(B){return w.unifiedExec(this,B)}}}}),C.addGrammarElement("styleLiteral",function(k,w,p){if(p.matchOpToken("{")){for(var q=[""],D=[];p.hasMore();){if(p.matchOpToken("\\"))p.consumeToken();else{if(p.matchOpToken("}"))break;if(p.matchToken("$")){var H=p.matchOpToken("{"),I=k.parseElement("expression",p);H&&p.requireOpToken("}"),D.push(I),q.push("")}else{var L=p.consumeToken();q[q.length-1]+=p.source.substring(L.start,L.end)}}q[q.length-1]+=p.lastWhitespace()}return{type:"styleLiteral",args:[D],op:function(F,B){var K="";return q.forEach(function(Y,X){K+=Y,X in B&&(K+=B[X])}),K},evaluate:function(F){return w.unifiedEval(this,F)}}}}),C.addCommand("remove",function(k,w,p){if(p.matchToken("remove")){var q=k.parseElement("classRef",p),D=null,H=null;if(q==null)D=k.parseElement("attributeRef",p),D==null&&(H=k.parseElement("expression",p),H==null&&k.raiseParseError(p,"Expected either a class reference, attribute expression or value expression"));else for(var I=[q];q=k.parseElement("classRef",p);)I.push(q);if(p.matchToken("from"))var L=k.requireElement("expression",p);else if(H==null)var L=k.requireElement("implicitMeTarget",p);return H?{elementExpr:H,from:L,args:[H,L],op:function(F,B,K){return w.nullCheck(B,H),w.implicitLoop(B,function(Y){Y.parentElement&&(K==null||K.contains(Y))&&Y.parentElement.removeChild(Y)}),w.findNext(this,F)}}:{classRefs:I,attributeRef:D,elementExpr:H,from:L,args:[I,L],op:function(F,B,K){return w.nullCheck(K,L),B?w.forEach(B,function(Y){w.implicitLoop(K,function(X){X.classList.remove(Y.className)})}):w.implicitLoop(K,function(Y){Y.removeAttribute(D.name)}),w.findNext(this,F)}}}}),C.addCommand("toggle",function(k,w,p){if(p.matchToken("toggle")){if(p.matchAnyToken("the","my"),p.currentToken().type==="STYLE_REF"){var q=p.consumeToken().value.substr(1),D=!0,H=y(k,p,q);if(p.matchToken("of")){p.pushFollow("with");try{var I=k.requireElement("expression",p)}finally{p.popFollow()}}else var I=k.requireElement("implicitMeTarget",p)}else if(p.matchToken("between")){var L=!0,F=k.parseElement("classRef",p);p.requireToken("and");var B=k.requireElement("classRef",p)}else{var F=k.parseElement("classRef",p),K=null;if(F==null)K=k.parseElement("attributeRef",p),K==null&&k.raiseParseError(p,"Expected either a class reference or attribute expression");else for(var Y=[F];F=k.parseElement("classRef",p);)Y.push(F)}if(D!==!0)if(p.matchToken("on"))var I=k.requireElement("expression",p);else var I=k.requireElement("implicitMeTarget",p);if(p.matchToken("for"))var X=k.requireElement("expression",p);else if(p.matchToken("until")){var c=k.requireElement("dotOrColonPath",p,"Expected event name");if(p.matchToken("from"))var f=k.requireElement("expression",p)}var i={classRef:F,classRef2:B,classRefs:Y,attributeRef:K,on:I,time:X,evt:c,from:f,toggle:function(l,v,m,g){w.nullCheck(l,I),D?w.implicitLoop(l,function(h){H("toggle",h)}):L?w.implicitLoop(l,function(h){h.classList.contains(v.className)?(h.classList.remove(v.className),h.classList.add(m.className)):(h.classList.add(v.className),h.classList.remove(m.className))}):g?w.forEach(g,function(h){w.implicitLoop(l,function(T){T.classList.toggle(h.className)})}):w.implicitLoop(l,function(h){h.hasAttribute(K.name)?h.removeAttribute(K.name):h.setAttribute(K.name,K.value)})},args:[I,X,c,f,F,B,Y],op:function(l,v,m,g,h,T,S,A){return m?new Promise(function(M){i.toggle(v,T,S,A),setTimeout(function(){i.toggle(v,T,S,A),M(w.findNext(i,l))},m)}):g?new Promise(function(M){var V=h||l.me;V.addEventListener(g,function(){i.toggle(v,T,S,A),M(w.findNext(i,l))},{once:!0}),i.toggle(v,T,S,A)}):(this.toggle(v,T,S,A),w.findNext(i,l))}};return i}});var s={display:function(k,w,p){if(p)w.style.display=p;else if(k==="toggle")getComputedStyle(w).display==="none"?s.display("show",w,p):s.display("hide",w,p);else if(k==="hide"){const q=C.runtime.getInternalData(w);q.originalDisplay==null&&(q.originalDisplay=w.style.display),w.style.display="none"}else{const q=C.runtime.getInternalData(w);q.originalDisplay&&q.originalDisplay!=="none"?w.style.display=q.originalDisplay:w.style.removeProperty("display")}},visibility:function(k,w,p){p?w.style.visibility=p:k==="toggle"?getComputedStyle(w).visibility==="hidden"?s.visibility("show",w,p):s.visibility("hide",w,p):k==="hide"?w.style.visibility="hidden":w.style.visibility="visible"},opacity:function(k,w,p){p?w.style.opacity=p:k==="toggle"?getComputedStyle(w).opacity==="0"?s.opacity("show",w,p):s.opacity("hide",w,p):k==="hide"?w.style.opacity="0":w.style.opacity="1"}},d=function(k,w,p){var q,D=p.currentToken();return D.value==="when"||D.value==="with"||k.commandBoundary(D)?q=k.parseElement("implicitMeTarget",p):q=k.parseElement("expression",p),q},y=function(k,w,p){var q=a.defaultHideShowStrategy,D=s;a.hideShowStrategies&&(D=Object.assign(D,a.hideShowStrategies)),p=p||q||"display";var H=D[p];return H==null&&k.raiseParseError(w,"Unknown show/hide strategy : "+p),H};C.addCommand("hide",function(k,w,p){if(p.matchToken("hide")){var q=d(k,w,p),D=null;p.matchToken("with")&&(D=p.requireTokenType("IDENTIFIER","STYLE_REF").value,D.indexOf("*")===0&&(D=D.substr(1)));var H=y(k,p,D);return{target:q,args:[q],op:function(I,L){return w.nullCheck(L,q),w.implicitLoop(L,function(F){H("hide",F)}),w.findNext(this,I)}}}}),C.addCommand("show",function(k,w,p){if(p.matchToken("show")){var q=d(k,w,p),D=null;p.matchToken("with")&&(D=p.requireTokenType("IDENTIFIER","STYLE_REF").value,D.indexOf("*")===0&&(D=D.substr(1)));var H=null;if(p.matchOpToken(":")){var I=p.consumeUntilWhitespace();p.matchTokenType("WHITESPACE"),H=I.map(function(B){return B.value}).join("")}if(p.matchToken("when"))var L=k.requireElement("expression",p);var F=y(k,p,D);return{target:q,when:L,args:[q],op:function(B,K){return w.nullCheck(K,q),w.implicitLoop(K,function(Y){L?(B.result=Y,w.evaluateNoPromise(L,B)?F("show",Y,H):F("hide",Y),B.result=null):F("show",Y,H)}),w.findNext(this,B)}}}}),C.addCommand("take",function(k,w,p){if(p.matchToken("take")){let F=null,B=[];for(;F=k.parseElement("classRef",p);)B.push(F);var q=null,D=null;let K=B.length>0;if(K||(q=k.parseElement("attributeRef",p),q==null&&k.raiseParseError(p,"Expected either a class reference or attribute expression"),p.matchToken("with")&&(D=k.requireElement("expression",p))),p.matchToken("from"))var H=k.requireElement("expression",p);if(p.matchToken("for"))var I=k.requireElement("expression",p);else var I=k.requireElement("implicitMeTarget",p);if(K){var L={classRefs:B,from:H,forElt:I,args:[B,H,I],op:function(Y,X,c,f){return w.nullCheck(f,I),w.implicitLoop(X,function(i){var l=i.className;c?w.implicitLoop(c,function(v){v.classList.remove(l)}):w.implicitLoop(i,function(v){v.classList.remove(l)}),w.implicitLoop(f,function(v){v.classList.add(l)})}),w.findNext(this,Y)}};return L}else{var L={attributeRef:q,from:H,forElt:I,args:[H,I,D],op:function(X,c,f,i){return w.nullCheck(c,H),w.nullCheck(f,I),w.implicitLoop(c,function(l){i?l.setAttribute(q.name,i):l.removeAttribute(q.name)}),w.implicitLoop(f,function(l){l.setAttribute(q.name,q.value||"")}),w.findNext(this,X)}};return L}}});function x(k,w,p,q){if(p!=null)var D=k.resolveSymbol(p,w);else var D=w;if(D instanceof Element||D instanceof HTMLDocument){for(;D.firstChild;)D.removeChild(D.firstChild);D.append(C.runtime.convertValue(q,"Fragment")),k.processNode(D)}else if(p!=null)k.setSymbol(p,w,null,q);else throw"Don't know how to put a value into "+typeof w}C.addCommand("put",function(k,w,p){if(p.matchToken("put")){var q=k.requireElement("expression",p),D=p.matchAnyToken("into","before","after");D==null&&p.matchToken("at")&&(p.matchToken("the"),D=p.matchAnyToken("start","end"),p.requireToken("of")),D==null&&k.raiseParseError(p,"Expected one of 'into', 'before', 'at start of', 'at end of', 'after'");var H=k.requireElement("expression",p),I=D.value,L=!1,F=!1,B=null,K=null;if(H.type==="arrayIndex"&&I==="into")L=!0,K=H.prop,B=H.root;else if(H.prop&&H.root&&I==="into")K=H.prop.value,B=H.root;else if(H.type==="symbol"&&I==="into")F=!0,K=H.name;else if(H.type==="attributeRef"&&I==="into"){var Y=!0;K=H.name,B=k.requireElement("implicitMeTarget",p)}else if(H.type==="styleRef"&&I==="into"){var X=!0;K=H.name,B=k.requireElement("implicitMeTarget",p)}else if(H.attribute&&I==="into"){var Y=H.attribute.type==="attributeRef",X=H.attribute.type==="styleRef";K=H.attribute.name,B=H.root}else B=H;var c={target:H,operation:I,symbolWrite:F,value:q,args:[B,K,q],op:function(f,i,l,v){if(F)x(w,f,l,v);else if(w.nullCheck(i,B),I==="into")Y?w.implicitLoop(i,function(g){g.setAttribute(l,v)}):X?w.implicitLoop(i,function(g){g.style[l]=v}):L?i[l]=v:w.implicitLoop(i,function(g){x(w,g,l,v)});else{var m=I==="before"?Element.prototype.before:I==="after"?Element.prototype.after:I==="start"?Element.prototype.prepend:Element.prototype.append;w.implicitLoop(i,function(g){m.call(g,v instanceof Node?v:w.convertValue(v,"Fragment")),g.parentElement?w.processNode(g.parentElement):w.processNode(g)})}return w.findNext(this,f)}};return c}});function R(k,w,p){var q;if(p.matchToken("the")||p.matchToken("element")||p.matchToken("elements")||p.currentToken().type==="CLASS_REF"||p.currentToken().type==="ID_REF"||p.currentToken().op&&p.currentToken().value==="<"){k.possessivesDisabled=!0;try{q=k.parseElement("expression",p)}finally{delete k.possessivesDisabled}p.matchOpToken("'")&&p.requireToken("s")}else if(p.currentToken().type==="IDENTIFIER"&&p.currentToken().value==="its"){var D=p.matchToken("its");q={type:"pseudopossessiveIts",token:D,name:D.value,evaluate:function(H){return w.resolveSymbol("it",H)}}}else p.matchToken("my")||p.matchToken("me"),q=k.parseElement("implicitMeTarget",p);return q}C.addCommand("transition",function(k,w,p){if(p.matchToken("transition")){for(var q=R(k,w,p),D=[],H=[],I=[],L=p.currentToken();!k.commandBoundary(L)&&L.value!=="over"&&L.value!=="using";){if(p.currentToken().type==="STYLE_REF"){let X=p.consumeToken().value.substr(1);D.push({type:"styleRefValue",evaluate:function(){return X}})}else D.push(k.requireElement("stringLike",p));p.matchToken("from")?H.push(k.requireElement("expression",p)):H.push(null),p.requireToken("to"),p.matchToken("initial")?I.push({type:"initial_literal",evaluate:function(){return"initial"}}):I.push(k.requireElement("expression",p)),L=p.currentToken()}if(p.matchToken("over"))var F=k.requireElement("expression",p);else if(p.matchToken("using"))var B=k.requireElement("expression",p);var K={to:I,args:[q,D,H,I,B,F],op:function(Y,X,c,f,i,l,v){w.nullCheck(X,q);var m=[];return w.implicitLoop(X,function(g){var h=new Promise(function(T,S){var A=g.style.transition;v?g.style.transition="all "+v+"ms ease-in":l?g.style.transition=l:g.style.transition=a.defaultTransition;for(var M=w.getInternalData(g),V=getComputedStyle(g),O={},N=0;N<V.length;N++){var G=V[N],U=V[G];O[G]=U}M.initialStyles||(M.initialStyles=O);for(var N=0;N<c.length;N++){var ne=c[N],ie=f[N];ie==="computed"||ie==null?g.style[ne]=O[ne]:g.style[ne]=ie}var Ee=!1,xe=!1;g.addEventListener("transitionend",function(){xe||(g.style.transition=A,xe=!0,T())},{once:!0}),g.addEventListener("transitionstart",function(){Ee=!0},{once:!0}),setTimeout(function(){!xe&&!Ee&&(g.style.transition=A,xe=!0,T())},100),setTimeout(function(){for(var ae=0;ae<c.length;ae++){var pe=c[ae],be=i[ae];if(be==="initial"){var ve=M.initialStyles[pe];g.style[pe]=ve}else g.style[pe]=be}},0)});m.push(h)}),Promise.all(m).then(function(){return w.findNext(K,Y)})}};return K}}),C.addCommand("measure",function(k,w,p){if(p.matchToken("measure")){var q=R(k,w,p),D=[];if(!k.commandBoundary(p.currentToken()))do D.push(p.matchTokenType("IDENTIFIER").value);while(p.matchOpToken(","));return{properties:D,args:[q],op:function(H,I){w.nullCheck(I,q),0 in I&&(I=I[0]);var L=I.getBoundingClientRect(),F={top:I.scrollTop,left:I.scrollLeft,topMax:I.scrollTopMax,leftMax:I.scrollLeftMax,height:I.scrollHeight,width:I.scrollWidth};return H.result={x:L.x,y:L.y,left:L.left,top:L.top,right:L.right,bottom:L.bottom,width:L.width,height:L.height,bounds:L,scrollLeft:F.left,scrollTop:F.top,scrollLeftMax:F.leftMax,scrollTopMax:F.topMax,scrollWidth:F.width,scrollHeight:F.height,scroll:F},w.forEach(D,function(B){if(B in H.result)H.locals[B]=H.result[B];else throw"No such measurement as "+B}),w.findNext(this,H)}}}}),C.addLeafExpression("closestExpr",function(k,w,p){if(p.matchToken("closest")){if(p.matchToken("parent"))var q=!0;var D=null;if(p.currentToken().type==="ATTRIBUTE_REF"){var H=k.requireElement("attributeRefAccess",p,null);D="["+H.attribute.name+"]"}if(D==null){var I=k.requireElement("expression",p);I.css==null?k.raiseParseError(p,"Expected a CSS expression"):D=I.css}if(p.matchToken("to"))var L=k.parseElement("expression",p);else var L=k.parseElement("implicitMeTarget",p);var F={type:"closestExpr",parentSearch:q,expr:I,css:D,to:L,args:[L],op:function(B,K){if(K==null)return null;{let Y=[];return w.implicitLoop(K,function(X){q?Y.push(X.parentElement?X.parentElement.closest(D):null):Y.push(X.closest(D))}),w.shouldAutoIterate(K)?Y:Y[0]}},evaluate:function(B){return w.unifiedEval(this,B)}};return H?(H.root=F,H.args=[F],H):F}}),C.addCommand("go",function(k,w,p){if(p.matchToken("go")){if(p.matchToken("back"))var q=!0;else if(p.matchToken("to"),p.matchToken("url")){var D=k.requireElement("stringLike",p),H=!0;if(p.matchToken("in")){p.requireToken("new"),p.requireToken("window");var I=!0}}else{p.matchToken("the");var L=p.matchAnyToken("top","middle","bottom"),F=p.matchAnyToken("left","center","right");(L||F)&&p.requireToken("of");var D=k.requireElement("unaryExpression",p),B=p.matchAnyOpToken("+","-");if(B){p.pushFollow("px");try{var K=k.requireElement("expression",p)}finally{p.popFollow()}}p.matchToken("px");var Y=p.matchAnyToken("smoothly","instantly"),X={block:"start",inline:"nearest"};L&&(L.value==="top"?X.block="start":L.value==="bottom"?X.block="end":L.value==="middle"&&(X.block="center")),F&&(F.value==="left"?X.inline="start":F.value==="center"?X.inline="center":F.value==="right"&&(X.inline="end")),Y&&(Y.value==="smoothly"?X.behavior="smooth":Y.value==="instantly"&&(X.behavior="instant"))}var c={target:D,args:[D,K],op:function(f,i,l){return q?window.history.back():H?i&&(I?window.open(i):window.location.href=i):w.implicitLoop(i,function(v){if(v===window&&(v=document.body),B){let m=v.getBoundingClientRect(),g=document.createElement("div"),h=B.value==="+"?l:l*-1,T=X.inline=="start"||X.inline=="end"?h:0,S=X.block=="start"||X.block=="end"?h:0;g.style.position="absolute",g.style.top=m.top+window.scrollY+S+"px",g.style.left=m.left+window.scrollX+T+"px",g.style.height=m.height+"px",g.style.width=m.width+"px",g.style.zIndex=""+Number.MIN_SAFE_INTEGER,g.style.opacity="0",document.body.appendChild(g),setTimeout(function(){document.body.removeChild(g)},100),v=g}v.scrollIntoView(X)}),w.findNext(c,f)}};return c}}),a.conversions.dynamicResolvers.push(function(k,w){if(!(k==="Values"||k.indexOf("Values:")===0))return;var p=k.split(":")[1],q={},D=C.runtime.implicitLoop.bind(C.runtime);if(D(w,function(L){var F=I(L);if(F!==void 0){q[F.name]=F.value;return}if(L.querySelectorAll!=null){var B=L.querySelectorAll("input,select,textarea");B.forEach(H)}}),p){if(p==="JSON")return JSON.stringify(q);if(p==="Form")return new URLSearchParams(q).toString();throw"Unknown conversion: "+p}else return q;function H(L){var F=I(L);if(F!=null){if(q[F.name]==null){q[F.name]=F.value;return}if(Array.isArray(q[F.name])&&Array.isArray(F.value)){q[F.name]=[].concat(q[F.name],F.value);return}}}function I(L){try{var F={name:L.name,value:L.value};if(F.name==null||F.value==null||L.type=="radio"&&L.checked==!1)return;if(L.type=="checkbox"&&(L.checked==!1?F.value=void 0:typeof F.value=="string"&&(F.value=[F.value])),L.type=="select-multiple"){var B=L.querySelectorAll("option[selected]");F.value=[];for(var K=0;K<B.length;K++)F.value.push(B[K].value)}return F}catch{return}}}),a.conversions.HTML=function(k){var w=function(p){if(p instanceof Array)return p.map(function(I){return w(I)}).join("");if(p instanceof HTMLElement)return p.outerHTML;if(p instanceof NodeList){for(var q="",D=0;D<p.length;D++){var H=p[D];H instanceof HTMLElement&&(q+=H.outerHTML)}return q}return p.toString?p.toString():""};return w(k)},a.conversions.Fragment=function(k){var w=document.createDocumentFragment();return C.runtime.implicitLoop(k,function(p){if(p instanceof Node)w.append(p);else{var q=document.createElement("template");q.innerHTML=p,w.append(q.content)}}),w}}const oe=new b,me=oe.lexer,le=oe.parser;function Se(C,s){return oe.evaluate(C,s)}function Ae(){var C=Array.from(n.document.querySelectorAll("script[type='text/hyperscript'][src]"));Promise.all(C.map(function(x){return fetch(x.src).then(function(R){return R.text()})})).then(x=>x.forEach(R=>Re(R))).then(()=>s(function(){y(),oe.processNode(document.documentElement),document.dispatchEvent(new Event("hyperscript:ready")),n.document.addEventListener("htmx:load",function(x){oe.processNode(x.detail.elt)})}));function s(x){document.readyState!=="loading"?setTimeout(x):document.addEventListener("DOMContentLoaded",x)}function d(){var x=document.querySelector('meta[name="htmx-config"]');return x?Q(x.content):null}function y(){var x=d();x&&Object.assign(a,x)}}const Re=Object.assign(Se,{config:a,use(C){C(Re)},internals:{lexer:me,parser:le,runtime:oe,Lexer:o,Tokens:u,Parser:E,Runtime:b},ElementCollection:re,addFeature:le.addFeature.bind(le),addCommand:le.addCommand.bind(le),addLeafExpression:le.addLeafExpression.bind(le),addIndirectExpression:le.addIndirectExpression.bind(le),evaluate:oe.evaluate.bind(oe),parse:oe.parse.bind(oe),processNode:oe.processNode.bind(oe),version:"0.9.14",browserInit:Ae});return Re})}(_hyperscript_min$1,_hyperscript_min$1.exports)),_hyperscript_min$1.exports}var _hyperscript_minExports=require_hyperscript_min();const _hyperscript=getDefaultExportFromCjs(_hyperscript_minExports);window.Alpine=module_default;module_default.start();window.htmx=htmx;window._hyperscript=_hyperscript;
