"""
Tests for WebSocket consumers.
"""

import json
from channels.testing import WebsocketCommunicator
from channels.db import database_sync_to_async
from django.test import TransactionTestCase
from django.contrib.auth import get_user_model
from apps.main.consumers import NotificationConsumer
from apps.main.models import Notification
from tests.factories.users import UserFactory

User = get_user_model()


class NotificationConsumerTestCase(TransactionTestCase):
    """
    Test case for the NotificationConsumer WebSocket consumer.
    """

    def setUp(self):
        """Set up test data."""
        self.user = UserFactory()

    async def test_authenticated_user_can_connect(self):
        """Test that authenticated users can connect to the WebSocket."""
        communicator = WebsocketCommunicator(
            NotificationConsumer.as_asgi(), "/ws/notifications/"
        )
        communicator.scope["user"] = self.user

        connected, subprotocol = await communicator.connect()
        self.assertTrue(connected)

        await communicator.disconnect()

    async def test_unauthenticated_user_cannot_connect(self):
        """Test that unauthenticated users cannot connect to the WebSocket."""
        from django.contrib.auth.models import AnonymousUser

        communicator = WebsocketCommunicator(
            NotificationConsumer.as_asgi(), "/ws/notifications/"
        )
        communicator.scope["user"] = AnonymousUser()

        connected, subprotocol = await communicator.connect()
        self.assertFalse(connected)

    async def test_notification_count_sent_on_connect(self):
        """Test that notification count is sent when user connects."""
        # Create some notifications for the user
        await self.create_notifications(3)

        communicator = WebsocketCommunicator(
            NotificationConsumer.as_asgi(), "/ws/notifications/"
        )
        communicator.scope["user"] = self.user

        connected, subprotocol = await communicator.connect()
        self.assertTrue(connected)

        # Should receive initial notification count
        response = await communicator.receive_json_from()
        self.assertEqual(response["type"], "notification_count")
        self.assertEqual(response["count"], 3)

        await communicator.disconnect()

    async def test_mark_notification_as_read_via_websocket(self):
        """Test marking a notification as read via WebSocket message."""
        # Create a notification
        notification = await self.create_notification()

        communicator = WebsocketCommunicator(
            NotificationConsumer.as_asgi(), "/ws/notifications/"
        )
        communicator.scope["user"] = self.user

        connected, subprotocol = await communicator.connect()
        self.assertTrue(connected)

        # Skip the initial count message
        await communicator.receive_json_from()

        # Send mark as read message
        await communicator.send_json_to(
            {"type": "mark_as_read", "notification_id": notification.id}
        )

        # Verify notification was marked as read
        await database_sync_to_async(notification.refresh_from_db)()
        self.assertTrue(notification.is_read)

        await communicator.disconnect()

    async def test_get_count_message(self):
        """Test requesting notification count via WebSocket."""
        await self.create_notifications(2)

        communicator = WebsocketCommunicator(
            NotificationConsumer.as_asgi(), "/ws/notifications/"
        )
        communicator.scope["user"] = self.user

        connected, subprotocol = await communicator.connect()
        self.assertTrue(connected)

        # Skip the initial count message
        await communicator.receive_json_from()

        # Request count
        await communicator.send_json_to({"type": "get_count"})

        # Should receive count response
        response = await communicator.receive_json_from()
        self.assertEqual(response["type"], "notification_count")
        self.assertEqual(response["count"], 2)

        await communicator.disconnect()

    async def test_new_notification_broadcast(self):
        """Test that new notifications are broadcast to connected users."""
        communicator = WebsocketCommunicator(
            NotificationConsumer.as_asgi(), "/ws/notifications/"
        )
        communicator.scope["user"] = self.user

        connected, subprotocol = await communicator.connect()
        self.assertTrue(connected)

        # Skip the initial count message
        await communicator.receive_json_from()

        # Create a new notification (this should trigger the lifecycle hook)
        notification = await self.create_notification(
            title="Real-time Test", message="This should appear instantly!"
        )

        # Should receive new notification message
        response = await communicator.receive_json_from()
        self.assertEqual(response["type"], "new_notification")
        self.assertEqual(response["notification"]["title"], "Real-time Test")
        self.assertEqual(response["notification"]["id"], notification.id)

        # Should also receive updated count
        count_response = await communicator.receive_json_from()
        self.assertEqual(count_response["type"], "notification_count")
        self.assertEqual(count_response["count"], 1)

        await communicator.disconnect()

    @database_sync_to_async
    def create_notification(self, title="Test Notification", message="Test message"):
        """Helper method to create a notification."""
        return Notification.objects.create(
            user=self.user,
            title=title,
            message=message,
            link="https://example.com",
            type="info",
        )

    @database_sync_to_async
    def create_notifications(self, count):
        """Helper method to create multiple notifications."""
        notifications = []
        for i in range(count):
            notification = Notification.objects.create(
                user=self.user,
                title=f"Test Notification {i + 1}",
                message=f"Test message {i + 1}",
                link="https://example.com",
                type="info",
            )
            notifications.append(notification)
        return notifications
