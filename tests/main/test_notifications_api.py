"""
Tests for the real-time notification API endpoints.
"""
from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.auth import get_user_model
from apps.main.models import Notification
from tests.factories.main import NotificationFactory
from tests.factories.users import UserFactory

User = get_user_model()


class NotificationAPITestCase(TestCase):
    """
    Test case for notification API endpoints.
    """

    def setUp(self):
        """Set up test data."""
        self.client = Client()
        self.user = UserFactory()
        self.other_user = UserFactory()
        
        # Create some notifications for the user
        self.notification1 = NotificationFactory(
            user=self.user,
            title="Test Notification 1",
            message="This is a test notification",
            type="info"
        )
        self.notification2 = NotificationFactory(
            user=self.user,
            title="Test Notification 2",
            message="This is another test notification",
            type="warning"
        )
        
        # Create a notification for another user (should not appear)
        self.other_notification = NotificationFactory(
            user=self.other_user,
            title="Other User Notification",
            message="This should not appear for our user",
            type="info"
        )

    def test_notifications_api_requires_login(self):
        """Test that the notifications API requires authentication."""
        url = reverse('notifications_api')
        response = self.client.get(url)
        self.assertEqual(response.status_code, 401)

    def test_notifications_count_api_requires_login(self):
        """Test that the notifications count API requires authentication."""
        url = reverse('notifications_count_api')
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.content.decode(), "0")

    def test_notifications_api_returns_user_notifications(self):
        """Test that the notifications API returns only the user's notifications."""
        self.client.force_login(self.user)
        url = reverse('notifications_api')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, 200)
        content = response.content.decode()
        
        # Should contain user's notifications
        self.assertIn("Test Notification 1", content)
        self.assertIn("Test Notification 2", content)
        
        # Should not contain other user's notifications
        self.assertNotIn("Other User Notification", content)

    def test_notifications_count_api_returns_correct_count(self):
        """Test that the notifications count API returns the correct count."""
        self.client.force_login(self.user)
        url = reverse('notifications_count_api')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, 200)
        content = response.content.decode()
        
        # Should show count of 2 for our user
        self.assertIn("2", content)

    def test_notifications_api_excludes_read_notifications(self):
        """Test that read notifications are excluded from the API response."""
        # Mark one notification as read
        self.notification1.mark_as_read()
        
        self.client.force_login(self.user)
        url = reverse('notifications_api')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, 200)
        content = response.content.decode()
        
        # Should not contain read notification
        self.assertNotIn("Test Notification 1", content)
        
        # Should still contain unread notification
        self.assertIn("Test Notification 2", content)

    def test_notifications_count_excludes_read_notifications(self):
        """Test that read notifications are excluded from the count."""
        # Mark one notification as read
        self.notification1.mark_as_read()
        
        self.client.force_login(self.user)
        url = reverse('notifications_count_api')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, 200)
        content = response.content.decode()
        
        # Should show count of 1 (only unread notification)
        self.assertIn("1", content)

    def test_notifications_api_orders_by_created_at_desc(self):
        """Test that notifications are ordered by creation date (newest first)."""
        self.client.force_login(self.user)
        url = reverse('notifications_api')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, 200)
        content = response.content.decode()
        
        # The newer notification should appear before the older one
        pos1 = content.find("Test Notification 2")
        pos2 = content.find("Test Notification 1")
        
        # notification2 was created after notification1, so it should appear first
        self.assertLess(pos1, pos2)

    def test_empty_notifications_shows_no_notifications_message(self):
        """Test that when there are no notifications, appropriate message is shown."""
        # Create a user with no notifications
        empty_user = UserFactory()
        self.client.force_login(empty_user)
        
        url = reverse('notifications_api')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, 200)
        content = response.content.decode()
        
        self.assertIn("No notifications at this time", content)

    def test_notification_types_display_correct_icons(self):
        """Test that different notification types display the correct icons."""
        # Create notifications of different types
        NotificationFactory(user=self.user, type="success", title="Success Notification")
        NotificationFactory(user=self.user, type="danger", title="Danger Notification")
        
        self.client.force_login(self.user)
        url = reverse('notifications_api')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, 200)
        content = response.content.decode()
        
        # Check for different icon classes
        self.assertIn("fa-info-circle text-blue-500", content)  # info
        self.assertIn("fa-exclamation-triangle text-yellow-500", content)  # warning
        self.assertIn("fa-check-circle text-green-500", content)  # success
        self.assertIn("fa-exclamation-circle text-red-500", content)  # danger
