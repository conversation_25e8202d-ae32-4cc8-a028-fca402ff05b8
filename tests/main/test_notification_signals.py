"""
Tests for notification lifecycle hooks and WebSocket functionality.
"""

import logging
from unittest.mock import patch, MagicMock
from django.test import TestCase
from apps.main.models import Notification
from tests.factories.users import UserFactory


class NotificationLifecycleTestCase(TestCase):
    """
    Test case for notification lifecycle hooks.
    """

    def setUp(self):
        """Set up test data."""
        self.user = UserFactory()

    @patch("apps.main.models.logger")
    @patch("apps.main.models.get_channel_layer")
    @patch("apps.main.models.async_to_sync")
    def test_notification_created_lifecycle_hook(
        self, mock_async_to_sync, mock_get_channel_layer, mock_logger
    ):
        """Test that the lifecycle hook logs and sends WebSocket message when a new notification is created."""
        # Mock the channel layer
        mock_channel_layer = MagicMock()
        mock_get_channel_layer.return_value = mock_channel_layer
        mock_group_send = MagicMock()
        mock_async_to_sync.return_value = mock_group_send

        # Create a new notification
        notification = Notification.objects.create(
            user=self.user,
            title="Test Notification",
            message="This is a test notification",
            link="https://example.com",
            type="info",
        )

        # Verify that the logger was called
        mock_logger.info.assert_any_call(
            f"New notification created for user {self.user.username}: Test Notification"
        )

        # Verify that WebSocket message was sent
        mock_group_send.assert_called_once_with(
            f"notifications_{self.user.id}",
            {
                "type": "notification_message",
                "message": {
                    "id": notification.id,
                    "title": "Test Notification",
                    "message": "This is a test notification",
                    "type": "info",
                    "link": "https://example.com",
                    "created_at": notification.created_at.isoformat(),
                },
            },
        )

    @patch("apps.main.models.logger")
    @patch("apps.main.models.get_channel_layer")
    def test_notification_updated_lifecycle_hook_not_triggered(
        self, mock_get_channel_layer, mock_logger
    ):
        """Test that the lifecycle hook is not triggered when a notification is updated."""
        # Create a notification
        notification = Notification.objects.create(
            user=self.user,
            title="Test Notification",
            message="This is a test notification",
            link="https://example.com",
            type="info",
        )

        # Clear the mock calls from creation
        mock_logger.reset_mock()
        mock_get_channel_layer.reset_mock()

        # Update the notification
        notification.title = "Updated Notification"
        notification.save()

        # Verify that the lifecycle hook was not called for the update
        # (AFTER_CREATE only fires on creation, not updates)
        mock_logger.info.assert_not_called()
        mock_get_channel_layer.assert_not_called()

    @patch("apps.main.models.logger")
    @patch("apps.main.models.get_channel_layer")
    def test_notification_websocket_error_handling(
        self, mock_get_channel_layer, mock_logger
    ):
        """Test that WebSocket errors are handled gracefully."""
        # Mock the channel layer to raise an exception
        mock_get_channel_layer.side_effect = Exception("WebSocket connection failed")

        # Create a new notification (should not raise an exception)
        notification = Notification.objects.create(
            user=self.user,
            title="Test Notification",
            message="This is a test notification",
            link="https://example.com",
            type="info",
        )

        # Verify that the error was logged
        mock_logger.error.assert_called_once_with(
            "Failed to send WebSocket notification: WebSocket connection failed"
        )

        # Verify that the notification was still created successfully
        self.assertTrue(Notification.objects.filter(id=notification.id).exists())

    def test_notification_inherits_from_lifecycle_model(self):
        """Test that the Notification model properly inherits from LifecycleModel."""
        from django_lifecycle import LifecycleModel

        # Check that Notification is a subclass of LifecycleModel
        self.assertTrue(issubclass(Notification, LifecycleModel))

        # Create a notification to ensure the model works correctly
        notification = Notification.objects.create(
            user=self.user,
            title="Test Notification",
            message="This is a test notification",
            link="https://example.com",
            type="info",
        )

        self.assertIsInstance(notification, Notification)
        self.assertIsInstance(notification, LifecycleModel)
