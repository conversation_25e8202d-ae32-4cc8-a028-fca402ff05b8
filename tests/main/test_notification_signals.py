"""
Tests for notification signal handlers.
"""
import logging
from unittest.mock import patch
from django.test import TestCase
from apps.main.models import Notification
from tests.factories.users import UserFactory


class NotificationSignalTestCase(TestCase):
    """
    Test case for notification signal handlers.
    """

    def setUp(self):
        """Set up test data."""
        self.user = UserFactory()

    @patch('apps.main.signals.logger')
    def test_notification_created_signal_logs_creation(self, mock_logger):
        """Test that the signal handler logs when a new notification is created."""
        # Create a new notification
        notification = Notification.objects.create(
            user=self.user,
            title="Test Notification",
            message="This is a test notification",
            link="https://example.com",
            type="info"
        )
        
        # Verify that the logger was called
        mock_logger.info.assert_called_once_with(
            f"New notification created for user {self.user.username}: Test Notification"
        )

    @patch('apps.main.signals.logger')
    def test_notification_updated_signal_does_not_log(self, mock_logger):
        """Test that the signal handler does not log when a notification is updated."""
        # Create a notification
        notification = Notification.objects.create(
            user=self.user,
            title="Test Notification",
            message="This is a test notification",
            link="https://example.com",
            type="info"
        )
        
        # Clear the mock calls from creation
        mock_logger.reset_mock()
        
        # Update the notification
        notification.title = "Updated Notification"
        notification.save()
        
        # Verify that the logger was not called for the update
        mock_logger.info.assert_not_called()

    def test_notification_signal_handler_exists(self):
        """Test that the signal handler is properly connected."""
        from django.db.models.signals import post_save
        from apps.main.signals import notification_created
        from apps.main.models import Notification
        
        # Check that our signal handler is connected
        receivers = post_save._live_receivers(sender=Notification)
        handler_functions = [receiver[1]() for receiver in receivers if receiver[1]() is not None]
        
        self.assertIn(notification_created, handler_functions)
