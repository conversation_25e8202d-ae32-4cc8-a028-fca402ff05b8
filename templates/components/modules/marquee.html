<section class="bg-white">
    <div class="py-14">
        <div class="container mx-auto flex w-full flex-col items-center justify-center p-4">
            <div class="relative flex w-full max-w-[1000px] flex-col items-center justify-center overflow-hidden rounded-[2rem] border py-14">
                <div class="absolute inset-0 rotate-[35deg] overflow-hidden" style="top: -50%; bottom: -50%;">
                    <div x-data="marquee()" class="flex flex-col gap-8">
                        <template x-for="(row, rowIndex) in rows" :key="rowIndex">
                            <div class="whitespace-nowrap">
                                <div class="inline-flex animate-marquee">
                                    <template x-for="(question, index) in [...row, ...row]" :key="index">
                                        <figure class="mx-4 cursor-pointer overflow-hidden rounded-[2rem] border p-4 opacity-85 bg-white">
                                            <blockquote class="text-sm text-center opacity-20" x-text="question"></blockquote>
                                        </figure>
                                    </template>
                                </div>
                            </div>
                        </template>
                    </div>
                </div>
                <div class="z-10 mt-4 flex flex-col items-center text-center text-black">
                    <h1 class="text-3xl font-bold mt-4 lg:text-4xl">Insert a call to action here!</h1>
                    <p class="mt-2">Get started for free. No credit card required.</p>
                    <a href="{% url 'account_signup' %}" class="inline-flex items-center justify-center text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 border border-input shadow-sm hover:bg-accent hover:text-accent-foreground h-10 group mt-4 rounded-[2rem] px-6 bg-white">
                        Get Started
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="ml-1 size-4 transition-all duration-300 ease-out group-hover:translate-x-1">
                            <path d="m9 18 6-6-6-6"></path>
                        </svg>
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>

<style>
    @keyframes marquee {
        0% { transform: translateX(0); }
        100% { transform: translateX(-100%); }
    }
    .animate-marquee {
        animation: marquee 30s linear infinite;
    }
</style>

<script>
    function marquee() {
        return {
            rows: [
                ["What is the capital of Switzerland?", "Who wrote '1984'?", "What's Heisenberg's Uncertainty Principle?"],
                ["What's the largest planet in our solar system?", "Who painted the Mona Lisa?", "What's the chemical symbol for gold?"],
                ["What's the fastest land animal?", "Who composed the 'Moonlight Sonata'?", "What's the boiling point of water in Celsius?"],
                ["What's the largest ocean on Earth?", "Who discovered penicillin?", "What's the speed of light in a vacuum?"],
                ["What's the smallest prime number?", "Who wrote 'To Kill a Mockingbird'?", "What's the atomic number of carbon?"],
                ["What's the capital of Japan?", "Who invented the telephone?", "What's the formula for the area of a circle?"]
            ]
        }
    }
</script>
