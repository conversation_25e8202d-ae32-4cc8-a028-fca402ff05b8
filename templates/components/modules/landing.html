{% load static %}

<section class="md:min-h-[100vh] bg-base-100 relative overflow-hidden">
    <div class="hidden md:block absolute inset-0 z-0">
        <svg xmlns="http://www.w3.org/2000/svg" class="opacity-[0.13]" viewBox="0 0 800 800">
            <defs>
                <radialGradient id="a" cx="400" cy="400" r="50%" gradientUnits="userSpaceOnUse">
                    <stop offset="0" stop-color="#ffffff"></stop>
                    <stop offset="1" stop-color="#06AB78"></stop>
                </radialGradient>
                <radialGradient id="b" cx="400" cy="400" r="70%" gradientUnits="userSpaceOnUse">
                    <stop offset="0" stop-color="#ffffff"></stop>
                    <stop offset="1" stop-color="#06AB78"></stop>
                </radialGradient>
            </defs>
            <rect fill="url(#a)" width="800" height="800"></rect>
            <g fill-opacity=".8" class="w-full">
                <path
                    fill="url(#b)"
                    d="M998.7 439.2c1.7-26.5 1.7-52.7 0.1-78.5L401 399.9c0 0 0-0.1 0-0.1l587.6-116.9c-5.1-25.9-11.9-51.2-20.3-75.8L400.9 399.7c0 0 0-0.1 0-0.1l537.3-265c-11.6-23.5-24.8-46.2-39.3-67.9L400.8 399.5c0 0 0-0.1-0.1-0.1l450.4-395c-17.3-19.7-35.8-38.2-55.5-55.5l-395 450.4c0 0-0.1 0-0.1-0.1L733.4-99c-21.7-14.5-44.4-27.6-68-39.3l-265 537.4c0 0-0.1 0-0.1 0l192.6-567.4c-24.6-8.3-49.9-15.1-75.8-20.2L400.2 399c0 0-0.1 0-0.1 0l39.2-597.7c-26.5-1.7-52.7-1.7-78.5-0.1L399.9 399c0 0-0.1 0-0.1 0L282.9-188.6c-25.9 5.1-51.2 11.9-75.8 20.3l192.6 567.4c0 0-0.1 0-0.1 0l-265-537.3c-23.5 11.6-46.2 24.8-67.9 39.3l332.8 498.1c0 0-0.1 0-0.1 0.1L4.4-51.1C-15.3-33.9-33.8-15.3-51.1 4.4l450.4 395c0 0 0 0.1-0.1 0.1L-99 66.6c-14.5 21.7-27.6 44.4-39.3 68l537.4 265c0 0 0 0.1 0 0.1l-567.4-192.6c-8.3 24.6-15.1 49.9-20.2 75.8L399 399.8c0 0 0 0.1 0 0.1l-597.7-39.2c-1.7 26.5-1.7 52.7-0.1 78.5L399 400.1c0 0 0 0.1 0 0.1l-587.6 116.9c5.1 25.9 11.9 51.2 20.3 75.8l567.4-192.6c0 0 0 0.1 0 0.1l-537.3 265c11.6 23.5 24.8 46.2 39.3 67.9l498.1-332.8c0 0 0 0.1 0.1 0.1l-450.4 395c17.3 19.7 35.8 38.2 55.5 55.5l395-450.4c0 0 0.1 0 0.1 0.1L66.6 899c21.7 14.5 44.4 27.6 68 39.3l265-537.4c0 0 0.1 0 0.1 0L207.1 968.3c24.6 8.3 49.9 15.1 75.8 20.2L399.8 401c0 0 0.1 0 0.1 0l-39.2 597.7c26.5 1.7 52.7 1.7 78.5 0.1L400.1 401c0 0 0.1 0 0.1 0l116.9 587.6c25.9-5.1 51.2-11.9 75.8-20.3L400.3 400.9c0 0 0.1 0 0.1 0l265 537.3c23.5-11.6 46.2-24.8 67.9-39.3L400.5 400.8c0 0 0.1 0 0.1-0.1l395 450.4c19.7-17.3 38.2-35.8 55.5-55.5l-450.4-395c0 0 0-0.1 0.1-0.1L899 733.4c14.5-21.7 27.6-44.4 39.3-68l-537.4-265c0 0 0-0.1 0-0.1l567.4 192.6c8.3-24.6 15.1-49.9 20.2-75.8L401 400.2c0 0 0-0.1 0-0.1L998.7 439.2z"
                ></path>
            </g>
        </svg>
    </div>
    <div class="relative max-w-5xl mx-auto flex flex-col items-center justify-center gap-16 lg:gap-20 px-8 py-12 lg:py-32">
        <div class="relative flex flex-col gap-10 lg:gap-12 items-center justify-center text-center">
            <div class="space-y-2">
                <h1 class="font-extrabold text-4xl lg:text-6xl tracking-tight md:-mb-4">
                    Show the world your businesses,<br />
                    not your resume
                </h1>
            </div>
            <p class="text-lg text-base-content-secondary leading-relaxed max-w-md mx-auto">Let people know you're a serious developer</p>
            <ul class="hidden md:block text-base-content-secondary leading-relaxed space-y-1">
                <li class="flex items-center justify-center lg:justify-start gap-2">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" class="w-[18px] h-[18px] text-primary">
                        <path fill-rule="evenodd" d="M16.704 4.153a.75.75 0 01.143 1.052l-8 10.5a.75.75 0 01-1.127.075l-4.5-4.5a.75.75 0 011.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 011.05-.143z" clip-rule="evenodd"></path>
                    </svg>
                    Compete against other developers
                </li>
                <li class="flex items-center justify-center lg:justify-start gap-2">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" class="w-[18px] h-[18px] text-primary">
                        <path fill-rule="evenodd" d="M16.704 4.153a.75.75 0 01.143 1.052l-8 10.5a.75.75 0 01-1.127.075l-4.5-4.5a.75.75 0 011.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 011.05-.143z" clip-rule="evenodd"></path>
                    </svg>
                    Reach more customers
                </li>
                <li class="flex items-center justify-center lg:justify-start gap-2">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" class="w-[18px] h-[18px] text-primary">
                        <path fill-rule="evenodd" d="M16.704 4.153a.75.75 0 01.143 1.052l-8 10.5a.75.75 0 01-1.127.075l-4.5-4.5a.75.75 0 011.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 011.05-.143z" clip-rule="evenodd"></path>
                    </svg>
                    Showcase your business
                </li>
            </ul>
            <a class="btn btn-primary btn-block group !btn-wide" href="{% url 'account_signup' %}">
                Sign Up
                <svg
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    class="w-[18px] h-[18px] fill-white/10 group-hover:translate-x-0.5 group-hover:fill-white/20 transition-transform duration-200"
                >
                    <path d="m3 3 3 9-3 9 19-9Z"></path>
                    <path d="M6 12h16"></path>
                </svg>
            </a>
            <div class="flex flex-col md:flex-row justify-center items-center md:items-start gap-3">
                <div class="-space-x-5 avatar-group justy-start">
                    <div class="avatar w-12 h-12">
                        <img
                            alt="user 1"
                            fetchpriority="high"
                            width="50"
                            height="50"
                            decoding="async"
                            data-nimg="1"
                            style="color: transparent;"
                            src="{% static 'images/user_image_1.png' %}"
                        />
                    </div>
                    <div class="avatar w-12 h-12">
                        <img
                            alt="Marc"
                            fetchpriority="high"
                            width="50"
                            height="50"
                            decoding="async"
                            data-nimg="1"
                            style="color: transparent;"
                            src="{% static 'images/user_image_2.jpg' %}"
                        />
                    </div>
                    <div class="avatar w-12 h-12">
                        <img
                            alt="Brian H Kang"
                            fetchpriority="high"
                            width="50"
                            height="50"
                            decoding="async"
                            data-nimg="1"
                            style="color: transparent;"
                            src="{% static 'images/user_image_3.jpg' %}"
                        />
                    </div>
                    <div class="avatar w-12 h-12">
                        <img
                            alt="Nico"
                            fetchpriority="high"
                            width="50"
                            height="50"
                            decoding="async"
                            data-nimg="1"
                            style="color: transparent;"
                            src="{% static 'images/user_image_4.png' %}"
                        />
                    </div>
                    <div class="avatar w-12 h-12">
                        <img
                            alt="Philip"
                            fetchpriority="high"
                            width="50"
                            height="50"
                            decoding="async"
                            data-nimg="1"
                            style="color: transparent;"
                            src="{% static 'images/user_image_5.jpeg' %}"
                        />
                    </div>
                </div>
                <div class="flex flex-col justify-center items-center md:items-start gap-1">
                    <div class="rating">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" class="w-5 h-5 text-yellow-500">
                            <path
                                fill-rule="evenodd"
                                d="M10.868 2.884c-.321-.772-1.415-.772-1.736 0l-1.83 4.401-4.753.381c-.833.067-1.171 1.107-.536 1.651l3.62 3.102-1.106 4.637c-.194.813.691 1.456 1.405 1.02L10 15.591l4.069 2.485c.713.436 1.598-.207 1.404-1.02l-1.106-4.637 3.62-3.102c.635-.544.297-1.584-.536-1.65l-4.752-.382-1.831-4.401z"
                                clip-rule="evenodd"
                            ></path>
                        </svg>
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" class="w-5 h-5 text-yellow-500">
                            <path
                                fill-rule="evenodd"
                                d="M10.868 2.884c-.321-.772-1.415-.772-1.736 0l-1.83 4.401-4.753.381c-.833.067-1.171 1.107-.536 1.651l3.62 3.102-1.106 4.637c-.194.813.691 1.456 1.405 1.02L10 15.591l4.069 2.485c.713.436 1.598-.207 1.404-1.02l-1.106-4.637 3.62-3.102c.635-.544.297-1.584-.536-1.65l-4.752-.382-1.831-4.401z"
                                clip-rule="evenodd"
                            ></path>
                        </svg>
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" class="w-5 h-5 text-yellow-500">
                            <path
                                fill-rule="evenodd"
                                d="M10.868 2.884c-.321-.772-1.415-.772-1.736 0l-1.83 4.401-4.753.381c-.833.067-1.171 1.107-.536 1.651l3.62 3.102-1.106 4.637c-.194.813.691 1.456 1.405 1.02L10 15.591l4.069 2.485c.713.436 1.598-.207 1.404-1.02l-1.106-4.637 3.62-3.102c.635-.544.297-1.584-.536-1.65l-4.752-.382-1.831-4.401z"
                                clip-rule="evenodd"
                            ></path>
                        </svg>
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" class="w-5 h-5 text-yellow-500">
                            <path
                                fill-rule="evenodd"
                                d="M10.868 2.884c-.321-.772-1.415-.772-1.736 0l-1.83 4.401-4.753.381c-.833.067-1.171 1.107-.536 1.651l3.62 3.102-1.106 4.637c-.194.813.691 1.456 1.405 1.02L10 15.591l4.069 2.485c.713.436 1.598-.207 1.404-1.02l-1.106-4.637 3.62-3.102c.635-.544.297-1.584-.536-1.65l-4.752-.382-1.831-4.401z"
                                clip-rule="evenodd"
                            ></path>
                        </svg>
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" class="w-5 h-5 text-yellow-500">
                            <path
                                fill-rule="evenodd"
                                d="M10.868 2.884c-.321-.772-1.415-.772-1.736 0l-1.83 4.401-4.753.381c-.833.067-1.171 1.107-.536 1.651l3.62 3.102-1.106 4.637c-.194.813.691 1.456 1.405 1.02L10 15.591l4.069 2.485c.713.436 1.598-.207 1.404-1.02l-1.106-4.637 3.62-3.102c.635-.544.297-1.584-.536-1.65l-4.752-.382-1.831-4.401z"
                                clip-rule="evenodd"
                            ></path>
                        </svg>
                    </div>
                    <div class="text-base text-base-content/80">
                        <span class="font-semibold text-base-content">{{ user_count|length }}</span>
                        <!-- -->serious developers are building.
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
