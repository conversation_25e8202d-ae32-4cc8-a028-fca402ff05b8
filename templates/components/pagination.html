{% load custom_filters %}

<div class="flex items-center justify-between mt-8">
    <div class="flex items-center space-x-4">
        {% if page_obj.has_previous %}
            <a href="?page={{ page_obj.previous_page_number }}" class="text-gray-700 hover:text-blue-600">Previous</a>
        {% else %}
            <span class="text-gray-400">Previous</span>
        {% endif %}

        {% for num in page_obj.paginator.page_range %}
            {% if num == page_obj.number %}
                <span class="w-8 h-8 flex items-center justify-center bg-blue-500 text-white rounded">{{ num }}</span>
            {% else %}
                <a href="?page={{ num }}" class="w-8 h-8 flex items-center justify-center text-gray-700 hover:text-blue-600">{{ num }}</a>
            {% endif %}
        {% endfor %}

        {% if page_obj.has_next %}
            <a href="?page={{ page_obj.next_page_number }}" class="text-gray-700 hover:text-blue-600">Next</a>
        {% else %}
            <span class="text-gray-400">Next</span>
        {% endif %}
    </div>
</div>

