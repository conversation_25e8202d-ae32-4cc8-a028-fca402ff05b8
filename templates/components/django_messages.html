{% comment %}
    Component for the django messages.

{% endcomment %}
<div id="django-messages-container">
    {% if messages %}
        <div x-data="{ showMessage: false }">
            {% for message in messages %}
                <div x-cloak class="absolute mx-auto left-0 right-0 mt-2 max-w-xl z-50 flex items-center
                                    {% if message.tags == 'error' %}bg-red-500{% else %}bg-amber-300{% endif %} text-white p-3 rounded-lg" role="alert"
                     x-show="showMessage"
                     x-init="setTimeout(() => showMessage = true, 200), setTimeout(() => showMessage = false, 6000)"
                     x-transition:enter="duration-700 ease-out"
                     x-transition:enter-start="opacity-0 -translate-y-5"
                     x-transition:enter-end="opacity-100 translate-y-0"
                     x-transition:leave="duration-200 ease-in"
                     x-transition:leave-start="opacity-100 translate-y-0"
                     x-transition:leave-end="opacity-0 -translate-y-5">
                    <svg class="w-8 h-8 mr-2"  viewBox="0 0 512 512">
                        <path d="M315.27,33,96,304H224L192.49,477.23a2.36,2.36,0,0,0,2.33,2.77h0a2.36,2.36,0,0,0,1.89-.95L416,208H288L319.66,34.75A2.45,2.45,0,0,0,317.22,32h0A2.42,2.42,0,0,0,315.27,33Z" style="fill:none;stroke:#ffffff;stroke-linecap:round;stroke-linejoin:round;stroke-width:32px" fill="white"></path>
                    </svg>
                    <div>
                        <div class="text-lg">
                            {% if message.text %}
                                {{ message.text }}
                            {% else %}
                                {{ message }}
                            {% endif %}
                        </div>
                    </div>
                    <div class="ml-auto cursor-pointer" @click="showMessage = false">
                        <svg fill="white" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" viewBox="0 0 24 24" stroke="currentColor" class="w-8 h-8 ml-2">
                            <path d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </div>
                </div>
            {% endfor %}
        </div>
    {% endif %}
</div>
