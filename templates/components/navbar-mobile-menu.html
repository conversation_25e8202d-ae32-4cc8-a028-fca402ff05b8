{% comment %}
    This component is for the navbar menu in mobile view
{% endcomment %}
{% load static %}
{% load waffle_tags %}  {# Ensure you have this tag loaded if you use it below #}

<!-- Mobile menu, controlled by Alpine.js -->
<div class="lg:hidden" x-data="{ openMenu: false }" role="dialog" aria-modal="true">
  <!-- Button to toggle menu -->
  <button @click="openMenu = !openMenu" class="-m-2.5 inline-flex items-center justify-center rounded-md p-2.5 text-gray-700">
    <span class="sr-only">Open main menu</span>
    <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true">
      <path stroke-linecap="round" stroke-linejoin="round" d="M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5" />
    </svg>
  </button>

  <!-- Menu content area -->
  <div x-show="openMenu" class="fixed inset-y-0 right-0 z-10 w-full overflow-y-auto bg-white px-6 py-6 sm:max-w-sm sm:ring-1 sm:ring-gray-900/10">
    <div class="flex items-center justify-between">
      <a href="{% url 'home' %}" class="-m-1.5 p-1.5">
        <span class="sr-only">Your Company</span>
        <img class="h-8 w-auto" src="{% static 'images/logo.png' %}" alt="">
      </a>
      <button @click="openMenu = false" type="button" class="-m-2.5 rounded-md p-2.5 text-gray-700">
        <span class="sr-only">Close menu</span>
        <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true">
          <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12" />
        </svg>
      </button>
    </div>

    <div class="mt-6 flow-root">
      <!-- Dynamic user profile link and logout if authenticated -->
      {% if user.is_authenticated %}
        <a href="#" class="block px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:text-gray-900 hover:bg-gray-50">My Profile</a>
        <form action="{% url 'account_logout' %}" method="post">
          {% csrf_token %}
          <button type="submit" class="block w-full text-left px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:text-gray-900 hover:bg-gray-50">
            Logout
          </button>
        </form>
      {% else %}
        <a href="{% url 'account_login' %}" class="block px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:text-gray-900 hover:bg-gray-50">Log In</a>
      {% endif %}
      <!-- Other links -->
      <a href="#" class="block px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:text-gray-900 hover:bg-gray-50">Settings</a>
    </div>
  </div>
</div>
