<!-- components/notifications_content.html -->
<!-- This template renders the dropdown content for notifications -->
{% if notifications %}
  {% for notification in notifications %}
    <li>
      <a class="block px-4 py-3 border-b border-gray-200 last:border-b-0" href="{{ notification.get_absolute_url }}" target="_blank">
        <div class="flex items-start">
          <div class="mr-3">
            {% if notification.type == 'info' %}
              <i class="fas fa-info-circle text-blue-500"></i>
            {% elif notification.type == 'warning' %}
              <i class="fas fa-exclamation-triangle text-yellow-500"></i>
            {% elif notification.type == 'danger' %}
              <i class="fas fa-exclamation-circle text-red-500"></i>
            {% elif notification.type == 'success' %}
              <i class="fas fa-check-circle text-green-500"></i>
            {% endif %}
          </div>
          <div class="flex-grow">
            <div class="font-semibold mb-1">{{ notification.title }}</div>
            <div class="text-sm mb-1">{{ notification.message|safe }}</div>
            <small class="text-gray-500">{{ notification.created_at|date:"SHORT_DATETIME_FORMAT" }}</small>
          </div>
        </div>
      </a>
    </li>
  {% endfor %}
{% else %}
  <li><a class="block px-4 py-3 text-center text-gray-500" href="#">No notifications at this time</a></li>
{% endif %}
