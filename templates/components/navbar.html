{% comment %}
    Header Template for the application
{% endcomment %}
{% load static %}
{% load waffle_tags %}

<header class="bg-white">
  <nav class="mx-auto flex max-w-7xl items-center justify-between p-6 lg:px-8" aria-label="Global">
    <div class="flex lg:flex-1">
      <a href="{% url 'home' %}" class="-m-1.5 p-1.5">
        <span class="sr-only">Your Company</span>
        <img class="h-8 w-auto" src="{% static 'images/logo.png'  %}" alt="">
      </a>
    </div>
    <div class="flex lg:hidden">
      {% include "components/navbar-mobile-menu.html" %}
    </div>
    <div class="hidden lg:flex lg:gap-x-12">
      <a href="#" class="text-sm font-semibold leading-6 text-gray-900">Features</a>
      <a href="#" class="text-sm font-semibold leading-6 text-gray-900">Marketplace</a>
      <a href="#" class="text-sm font-semibold leading-6 text-gray-900">Company</a>
    </div>
    <div class="hidden lg:flex lg:flex-1 lg:justify-end">
      {% if user.is_authenticated %}
        <div class="relative flex items-center">
          <!-- Include Notifications Dropdown -->
          {% include "components/notifications.html" %}
          <div class="dropdown dropdown-end ml-4">
            <label tabindex="0" class="btn btn-ghost btn-circle avatar">
              <div class="w-10 rounded-full">
                <img src="{{ user.avatar_url }}" alt="{{ user.username }}" class="object-cover">
              </div>
            </label>
            <ul tabindex="0" class="dropdown-content menu p-2 shadow bg-base-100 rounded-box w-52 z-20">
              <li><a href="#">My Profile</a></li>
              <li><a href="{% url 'account_logout' %}">Log Out</a></li>
            </ul>
          </div>
        </div>
      {% else %}
        <a href="{% url 'account_login' %}" class="text-sm font-semibold leading-6 text-gray-900">Log in <span aria-hidden="true">&rarr;</span></a>
      {% endif %}
    </div>
  </nav>
</header>
