<div x-data="{ open: false }">
  <!-- <PERSON>ton trigger for modal -->
  <button @click="open = true" class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded inline-flex items-center">
    <i class="fas fa-flag"></i>
  </button>

  <!-- Modal -->
  <div x-show="open" @keydown.escape.window="open = false" class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-labelledby="reportModalLabel" aria-modal="true" role="dialog">
    <div class="flex items-center justify-center min-h-screen">
      <div class="bg-white rounded-lg overflow-hidden shadow-xl transform transition-all max-w-lg w-full" @click.away="open = false">
        <form method="POST" action="{{ object.report_url }}">
          {% csrf_token %}
          <div class="bg-red-600 text-white px-4 py-2 flex justify-between items-center">
            <h5 class="text-xl font-medium"><i class="fas fa-flag mr-2"></i>Report {{ model_type }}</h5>
            <button type="button" @click="open = false" class="text-white">
              <span class="text-2xl">&times;</span>
            </button>
          </div>
          <div class="p-6">
            <div class="mb-4">
              {{ report_form.reason.label_tag }}
              {{ report_form.reason }}
            </div>
          </div>
          <div class="px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
            <button type="submit" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm">
              Submit Report
            </button>
            <button type="button" @click="open = false" class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
              Cancel
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>
