<!-- components/notifications.html -->
<div class="relative px-5"
     x-data="notificationComponent({{ notifications|length|default:0 }})"
     x-init="initWebSocket()">
  <div class="dropdown dropdown-end">
    <label tabindex="0"
           class="cursor-pointer relative block bg-blue-600 text-white p-2 rounded-md focus:outline-none transition-all duration-200"
           :class="{ 'animate-pulse': hasNewNotifications, 'ring-2 ring-yellow-400': hasNewNotifications }"
           @click="toggleDropdown()">
      <i class="fas fa-bell"></i>
      <!-- Notification badge with WebSocket updates and HTMX fallback -->
      <div id="notification-badge"
           x-show="notificationCount > 0"
           hx-get="{% url 'notifications_count_api' %}"
           hx-trigger="load, every 30s"
           hx-swap="innerHTML">
        <span class="absolute top-0 right-0 transform translate-x-1/2 -translate-y-1/2 bg-red-600 text-white text-xs font-bold rounded-full px-2 py-1">
          <span x-text="notificationCount >= 100 ? '99+' : notificationCount"></span>
          <span class="sr-only">unread messages</span>
        </span>
      </div>
    </label>

    <!-- Dropdown content -->
    <ul tabindex="0"
        class="dropdown-content menu p-2 shadow bg-base-100 rounded-box w-72 mt-2 overflow-hidden max-h-96 overflow-y-auto"
        x-show="isOpen"
        x-transition:enter="transition ease-out duration-200"
        x-transition:enter-start="opacity-0 scale-95"
        x-transition:enter-end="opacity-100 scale-100"
        x-transition:leave="transition ease-in duration-150"
        x-transition:leave-start="opacity-100 scale-100"
        x-transition:leave-end="opacity-0 scale-95"
        @click.away="isOpen = false">

      <!-- Notifications content with WebSocket updates and HTMX fallback -->
      <div id="notifications-content"
           hx-get="{% url 'notifications_api' %}"
           hx-trigger="load, refreshNotifications from:body, every 60s"
           hx-swap="innerHTML">
        {% include 'components/notifications_content.html' %}
      </div>
    </ul>
  </div>
</div>
