<!-- components/notifications.html -->
<div class="relative px-5"
     x-data="{
       isOpen: false,
       hasNewNotifications: false,
       lastNotificationCount: {{ notifications|length|default:0 }}
     }"
     x-init="
       // Watch for changes in notification count
       $watch('lastNotificationCount', (newCount, oldCount) => {
         if (oldCount !== undefined && newCount > oldCount) {
           hasNewNotifications = true;
           // Reset the flag after 3 seconds
           setTimeout(() => hasNewNotifications = false, 3000);
         }
       });
     ">
  <div class="dropdown dropdown-end">
    <label tabindex="0"
           class="cursor-pointer relative block bg-blue-600 text-white p-2 rounded-md focus:outline-none transition-all duration-200"
           :class="{ 'animate-pulse': hasNewNotifications }"
           @click="isOpen = !isOpen">
      <i class="fas fa-bell"></i>
      <!-- Notification badge with HTMX polling -->
      <div id="notification-badge"
           hx-get="{% url 'notifications_count_api' %}"
           hx-trigger="load, every 5s"
           hx-swap="innerHTML">
        {% with notifications_count=notifications|length %}
          {% include 'components/notification_badge.html' %}
        {% endwith %}
      </div>
    </label>

    <!-- Dropdown content -->
    <ul tabindex="0"
        class="dropdown-content menu p-2 shadow bg-base-100 rounded-box w-72 mt-2 overflow-hidden max-h-96 overflow-y-auto"
        x-show="isOpen"
        x-transition:enter="transition ease-out duration-200"
        x-transition:enter-start="opacity-0 scale-95"
        x-transition:enter-end="opacity-100 scale-100"
        x-transition:leave="transition ease-in duration-150"
        x-transition:leave-start="opacity-100 scale-100"
        x-transition:leave-end="opacity-0 scale-95"
        @click.away="isOpen = false">

      <!-- Notifications content with HTMX polling -->
      <div id="notifications-content"
           hx-get="{% url 'notifications_api' %}"
           hx-trigger="load, every 10s"
           hx-swap="innerHTML">
        {% include 'components/notifications_content.html' %}
      </div>
    </ul>
  </div>
</div>
