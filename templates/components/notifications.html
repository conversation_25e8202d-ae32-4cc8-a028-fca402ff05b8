<!-- components/notifications.html -->
<div class="relative px-5">
  <div class="dropdown dropdown-end">
    <label tabindex="0" class="cursor-pointer relative block bg-blue-600 text-white p-2 rounded-md focus:outline-none">
      <i class="fas fa-bell"></i>
      {% if notifications %}
        <span class="absolute top-0 right-0 transform translate-x-1/2 -translate-y-1/2 bg-red-600 text-white text-xs font-bold rounded-full px-2 py-1">
          {% if notifications|length >= 100 %}
            99+
          {% else %}
            {{ notifications|length }}
          {% endif %}
          <span class="sr-only">unread messages</span>
        </span>
      {% endif %}
    </label>
    <ul tabindex="0" class="dropdown-content menu p-2 shadow bg-base-100 rounded-box w-72 mt-2 overflow-hidden">
      {% if notifications %}
        {% for notification in notifications %}
          <li>
            <a class="block px-4 py-3 border-b border-gray-200 last:border-b-0" href="{{ notification.get_absolute_url }}" target="_blank">
              <div class="flex items-start">
                <div class="mr-3">
                  {% if notification.type == 'info' %}
                    <i class="fas fa-info-circle text-blue-500"></i>
                  {% elif notification.type == 'warning' %}
                    <i class="fas fa-exclamation-triangle text-yellow-500"></i>
                  {% elif notification.type == 'danger' %}
                    <i class="fas fa-exclamation-circle text-red-500"></i>
                  {% elif notification.type == 'success' %}
                    <i class="fas fa-check-circle text-green-500"></i>
                  {% endif %}
                </div>
                <div class="flex-grow">
                  <div class="font-semibold mb-1">{{ notification.title }}</div>
                  <div class="text-sm mb-1">{{ notification.message|safe }}</div>
                  <small class="text-gray-500">{{ notification.timestamp|date:"SHORT_DATETIME_FORMAT" }}</small>
                </div>
              </div>
            </a>
          </li>
        {% endfor %}
      {% else %}
        <li><a class="block px-4 py-3 text-center text-gray-500" href="#">No notifications at this time</a></li>
      {% endif %}
    </ul>
  </div>
</div>
