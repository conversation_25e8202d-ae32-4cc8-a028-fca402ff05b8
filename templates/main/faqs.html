{% extends 'base.html' %}

{% block content %}
    {{ block.super }}

    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12 bg-white shadow-lg rounded-lg mt-5">
        <h2 class="text-2xl font-semibold text-gray-800 mb-6">Frequently Asked Questions</h2>
        <div class="divide-y divide-gray-200">
            {% for faq in faqs %}
                <div x-data="{ open: false }">
                    <h3 class="text-lg leading-6 font-medium text-gray-900 my-5">
                        <button @click="open = !open" class="w-full text-left">
                            {{ faq.question|safe }}
                        </button>
                    </h3>
                    <div x-show="open" style="display: none;" class="mt-2 pr-4">
                        <p class="text-gray-500">{{ faq.answer|safe }}</p>
                    </div>
                </div>
            {% endfor %}
        </div>
    </div>

{% endblock %}
